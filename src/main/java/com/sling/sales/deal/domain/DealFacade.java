package com.sling.sales.deal.domain;

import static com.sling.sales.deal.api.response.RequestType.JSON_PATCH;
import static com.sling.sales.deal.common.dto.EntityType.DEAL;
import static com.sling.sales.deal.common.dto.EntityType.MEETING;
import static com.sling.sales.deal.common.dto.EntityType.USER;
import static com.sling.sales.deal.domain.DealSpecifications.associatedWithPipelineId;
import static com.sling.sales.deal.domain.DealSpecifications.associatedWithProduct;
import static com.sling.sales.deal.domain.DealSpecifications.belongToTenant;
import static com.sling.sales.deal.domain.DealSpecifications.containsName;
import static com.sling.sales.deal.domain.DealSpecifications.createdBy;
import static com.sling.sales.deal.domain.DealSpecifications.haveDealOwnersIn;
import static com.sling.sales.deal.domain.DealSpecifications.haveIdsIn;
import static com.sling.sales.deal.domain.DealSpecifications.isAssociatedWithCompany;
import static com.sling.sales.deal.domain.DealSpecifications.isAssociatedWithContact;
import static com.sling.sales.deal.domain.DealSpecifications.modifiedBy;
import static com.sling.sales.deal.domain.DealSpecifications.ownedBy;
import static com.sling.sales.deal.domain.DealSpecifications.withDealId;
import static com.sling.sales.deal.domain.pipeline.StageTypes.CLOSED_WON;
import static com.sling.sales.deal.domain.pipeline.StageTypes.OPEN;
import static com.sling.sales.deal.mq.event.DealEventPayloadV2.toDealRequestV2;
import static com.sling.sales.deal.mq.event.Metadata.Action.CREATED;
import static com.sling.sales.deal.mq.event.Metadata.Action.DELETED;
import static com.sling.sales.deal.mq.event.Metadata.Action.UPDATED;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.emptySet;
import static java.util.Collections.singletonList;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;
import static java.util.stream.Collectors.toUnmodifiableList;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import com.sling.sales.deal.api.request.DealRequest;
import com.sling.sales.deal.api.request.Pipeline;
import com.sling.sales.deal.api.request.ShareRuleForSingleDealRequest;
import com.sling.sales.deal.api.request.ShareRuleRequest;
import com.sling.sales.deal.api.request.StageActivationRequest;
import com.sling.sales.deal.api.response.DealImportRequest;
import com.sling.sales.deal.api.response.DeleteDetail;
import com.sling.sales.deal.api.response.EstimatedValue;
import com.sling.sales.deal.api.response.ReassignDetail;
import com.sling.sales.deal.api.response.RequestType;
import com.sling.sales.deal.api.response.ResultType;
import com.sling.sales.deal.api.response.ShareRuleSummary;
import com.sling.sales.deal.api.response.TenantCurrencyResponse;
import com.sling.sales.deal.common.dto.Category;
import com.sling.sales.deal.common.dto.Discount;
import com.sling.sales.deal.common.dto.Discount.Type;
import com.sling.sales.deal.common.dto.FieldOperation;
import com.sling.sales.deal.common.dto.MetaInfo;
import com.sling.sales.deal.common.dto.ProductUnit;
import com.sling.sales.deal.common.dto.Unit;
import com.sling.sales.deal.domain.deal.Company;
import com.sling.sales.deal.domain.deal.Contact;
import com.sling.sales.deal.domain.deal.Deal;
import com.sling.sales.deal.domain.deal.DealProduct;
import com.sling.sales.deal.domain.deal.DealUtm;
import com.sling.sales.deal.domain.deal.ForexMoney;
import com.sling.sales.deal.domain.deal.LookUp;
import com.sling.sales.deal.domain.deal.Money;
import com.sling.sales.deal.domain.deal.Product;
import com.sling.sales.deal.domain.exception.DealNotFoundException;
import com.sling.sales.deal.domain.exception.InactiveProductException;
import com.sling.sales.deal.domain.exception.InsufficientPrivilegeException;
import com.sling.sales.deal.domain.exception.InvalidPicklistValueException;
import com.sling.sales.deal.domain.exception.InvalidPipelineException;
import com.sling.sales.deal.domain.field.Field;
import com.sling.sales.deal.domain.field.FieldService;
import com.sling.sales.deal.domain.field.PicklistValue;
import com.sling.sales.deal.domain.picklistvalue.PicklistValueFacade;
import com.sling.sales.deal.domain.pipeline.DealPipelineFacade;
import com.sling.sales.deal.domain.pipeline.PipelineStage;
import com.sling.sales.deal.domain.pipeline.StageTypes;
import com.sling.sales.deal.domain.service.CompanyService;
import com.sling.sales.deal.domain.service.ContactService;
import com.sling.sales.deal.domain.service.PipelineResponse;
import com.sling.sales.deal.domain.service.PipelineService;
import com.sling.sales.deal.domain.service.PipelineStageResponse;
import com.sling.sales.deal.domain.service.ProductCacheService;
import com.sling.sales.deal.domain.service.ProductService;
import com.sling.sales.deal.domain.service.UserService;
import com.sling.sales.deal.domain.share.DealSharingFacade;
import com.sling.sales.deal.domain.share.EntityShareRuleService;
import com.sling.sales.deal.domain.share.ReportingManagerShareRuleDeleteEvent;
import com.sling.sales.deal.domain.share.ShareRule;
import com.sling.sales.deal.domain.user.Action;
import com.sling.sales.deal.domain.user.Tenant;
import com.sling.sales.deal.domain.user.User;
import com.sling.sales.deal.domain.user.UserFacade;
import com.sling.sales.deal.error.DomainException;
import com.sling.sales.deal.error.ErrorCode;
import com.sling.sales.deal.error.ErrorResource;
import com.sling.sales.deal.error.ResourceNotFoundException;
import com.sling.sales.deal.forex.ForexService;
import com.sling.sales.deal.forex.response.ExchangeRateResponse;
import com.sling.sales.deal.infra.Currency;
import com.sling.sales.deal.infra.CurrencyService;
import com.sling.sales.deal.layout.api.response.list.FieldType;
import com.sling.sales.deal.layout.api.response.list.ProductFieldResponse;
import com.sling.sales.deal.mq.DealEventPublisher;
import com.sling.sales.deal.mq.DealEventPublisherV2;
import com.sling.sales.deal.mq.TenantUsageInternalPublisher;
import com.sling.sales.deal.mq.event.CompanyNameUpdatedEvent;
import com.sling.sales.deal.mq.event.ContactNameUpdatedEvent;
import com.sling.sales.deal.mq.event.DealAssociatedContactEvent;
import com.sling.sales.deal.mq.event.DealCreatedEvent;
import com.sling.sales.deal.mq.event.DealDeletedEvent;
import com.sling.sales.deal.mq.event.DealEventPayloadV2;
import com.sling.sales.deal.mq.event.DealEventPublisherV3;
import com.sling.sales.deal.mq.event.DealNameUpdatedEvent;
import com.sling.sales.deal.mq.event.DealReassignedEvent;
import com.sling.sales.deal.mq.event.DealRequestV2;
import com.sling.sales.deal.mq.event.DealUpdatedEvent;
import com.sling.sales.deal.mq.event.ExchangeRateHistoryAddedEvent;
import com.sling.sales.deal.mq.event.IdName;
import com.sling.sales.deal.mq.event.Metadata;
import com.sling.sales.deal.mq.event.PicklistValueUpdateEventDetail;
import com.sling.sales.deal.mq.event.PipelinePayload;
import com.sling.sales.deal.mq.event.PipelineStagePayload;
import com.sling.sales.deal.mq.event.PipelineUpdatedEvent;
import com.sling.sales.deal.mq.event.PipelineUpdatedEventV2;
import com.sling.sales.deal.mq.event.ProductNameUpdatedEvent;
import com.sling.sales.deal.mq.event.TenantUsageByEntity;
import com.sling.sales.deal.mq.event.UserNameUpdatedEvent;
import com.sling.sales.deal.security.AuthService;
import com.sling.sales.deal.security.jwt.Source;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple7;
import reactor.util.function.Tuples;

@Service
@Slf4j
public class DealFacade {

  private final AuthService authService;
  private final UserService userService;
  private final DealRepository dealRepository;
  private final ContactService contactService;
  private final ContactRepository contactRepository;
  private final DealEventPublisher dealEventPublisher;
  private final DealEventPublisherV2 dealEventPublisherV2;
  private final DealProductRepository dealProductRepository;
  private final CompanyRepository companyRepository;
  private final ProductService productService;
  private final DealPipelineFacade dealPipelineFacade;
  private final CompanyService companyService;
  private final DealSharingFacade dealSharingFacade;
  private final UserFacade userFacade;

  private final DealLookUpRepository dealLookUpRepository;
  private final EntityShareRuleService entityShareRuleService;
  private final PicklistValueFacade picklistValueFacade;
  private final FieldService fieldService;
  private final CurrencyService currencyService;
  private final EntityManager entityManager;
  private final PipelineService pipelineService;
  private final ForexService forexService;
  private final TenantUsageInternalPublisher tenantUsageInternalPublisher;
  private final DealEventPublisherV3 dealEventPublisherV3;
  private final ProductCacheService productCacheService;


  @Autowired
  public DealFacade(
      AuthService authService,
      UserService userService,
      DealRepository dealRepository,
      ContactService contactService,
      ContactRepository contactRepository,
      DealProductRepository dealProductRepository,
      DealEventPublisher dealEventPublisher,
      DealEventPublisherV2 dealEventPublisherV2,
      ProductService productService,
      DealPipelineFacade dealPipelineFacade,
      CompanyRepository companyRepository,
      CompanyService companyService,
      DealSharingFacade dealSharingFacade,
      UserFacade userFacade, DealLookUpRepository dealLookUpRepository,
      EntityShareRuleService entityShareRuleService, PicklistValueFacade picklistValueFacade,
      FieldService fieldService, CurrencyService currencyService, EntityManager entityManager,
      PipelineService pipelineService, ForexService forexService,
      TenantUsageInternalPublisher tenantUsageInternalPublisher, DealEventPublisherV3 dealEventPublisherV3,
      ProductCacheService productCacheService) {
    this.authService = authService;
    this.userService = userService;
    this.dealRepository = dealRepository;
    this.contactService = contactService;
    this.contactRepository = contactRepository;
    this.dealProductRepository = dealProductRepository;
    this.dealEventPublisher = dealEventPublisher;
    this.dealEventPublisherV2 = dealEventPublisherV2;
    this.productService = productService;
    this.dealPipelineFacade = dealPipelineFacade;
    this.companyRepository = companyRepository;
    this.companyService = companyService;
    this.dealSharingFacade = dealSharingFacade;
    this.userFacade = userFacade;
    this.dealLookUpRepository = dealLookUpRepository;
    this.entityShareRuleService = entityShareRuleService;
    this.picklistValueFacade = picklistValueFacade;
    this.fieldService = fieldService;
    this.currencyService = currencyService;
    this.entityManager = entityManager;
    this.pipelineService = pipelineService;
    this.forexService = forexService;
    this.tenantUsageInternalPublisher = tenantUsageInternalPublisher;
    this.dealEventPublisherV3 = dealEventPublisherV3;
    this.productCacheService = productCacheService;
  }


  @Transactional
  public Deal createDeal(DealRequest dealToCreate) {
    dealToCreate.validateCreatedAtUpdatedAt();
    var loggedInUser = authService.getLoggedInUser();
    var authenticationToken = authService.getAuthenticationToken();
    var sourceMetaInfo = authService.getSource();
    User loggedInUserDetails = userService.getUserDetails(loggedInUser.getId(), authenticationToken).block();
    User createdBy =
        ObjectUtils.isEmpty(dealToCreate.getCreatedBy()) || dealToCreate.getCreatedBy().getId() == loggedInUser.getId() ? loggedInUserDetails
            : setCreatedByUpdatedBy(dealToCreate.getCreatedBy(), loggedInUser, authenticationToken, ErrorCode.CREATED_BY_USER_NOT_PRESENT);

    User updatedBy =
        ObjectUtils.isEmpty(dealToCreate.getUpdatedBy()) || dealToCreate.getUpdatedBy().getId() == loggedInUser.getId() ? loggedInUserDetails
            : setCreatedByUpdatedBy(dealToCreate.getUpdatedBy(), loggedInUser, authenticationToken, ErrorCode.UPDATED_BY_USER_NOT_PRESENT);

    Tuple7<User, Set<Contact>, List<Product>, List<PipelineStage>, Optional<Company>, List<TenantCurrencyResponse>, Currency> responseTuple = getDetailsOfDealPropertiesFromRequest(
        dealToCreate, loggedInUser, authenticationToken, null, null);

    Deal createdDeal = createDeal(
        dealToCreate,
        loggedInUserDetails,
        sourceMetaInfo,
        responseTuple,
        createdBy,
        updatedBy,
        getForexResponse(authenticationToken).block(),
        null,
        getProductFieldResponse(authenticationToken, dealToCreate.getProducts(), loggedInUser.getTenantId()).block());

    return saveNewDealAndRaiseEvents(createdDeal, loggedInUser, responseTuple.getT6());

  }


  private Mono<List<ProductFieldResponse>> getProductFieldResponse(String authenticationToken,
      List<com.sling.sales.deal.common.dto.Product> dealProducts, Long tenantId) {
    if (ObjectUtils.isEmpty(dealProducts)) {
      return Mono.just(Collections.emptyList());
    }
    return productCacheService.getProductFields(authenticationToken, tenantId);
  }

  private Mono<ExchangeRateResponse> getForexResponse(String authenticationToken) {
    return forexService.getCurrentDateExchangeRates(authenticationToken);
  }

  private Mono<Deal> createDealViaImport(DealRequest dealToCreate, User loggedInUser, String authenticationToken, Source sourceMetaInfo,
      Mono<User> createdBy, Mono<User> updatedBy) {
    Mono<ExchangeRateResponse> currentExchangeRateResponse = getForexResponse(authenticationToken);
    Mono<ExchangeRateResponse> exchangeRateResponseByDate = dealToCreate.getActualClosureDate() == null ? currentExchangeRateResponse
        : forexService.getExchangeRateByDate(authenticationToken, dealToCreate.getActualClosureDate());
    Mono<User> loggedInUserDetails = userService.getUserDetails(loggedInUser.getId(), authenticationToken);
    return Mono.zip(getDetailsOfDealPropertiesFromImportRequest(dealToCreate, loggedInUser, authenticationToken, null), loggedInUserDetails,
            createdBy, updatedBy, currentExchangeRateResponse, exchangeRateResponseByDate,
            getProductFieldResponse(authenticationToken, dealToCreate.getProducts(), loggedInUser.getTenantId()))
        .map(responseTuple -> {
          Deal createdDeal = createDeal(dealToCreate, responseTuple.getT2(), sourceMetaInfo, responseTuple.getT1(), responseTuple.getT3(),
              responseTuple.getT4(), responseTuple.getT5(), responseTuple.getT6(), responseTuple.getT7());
          return saveNewDealAndRaiseEvents(createdDeal, loggedInUser, responseTuple.getT1().getT6());
        });
  }

  @Transactional
  public Mono<Deal> createDealViaImport(DealImportRequest dealImportRequest) {

    Map<String, Object> importRequest = dealImportRequest.getDeal();
    com.sling.sales.deal.domain.user.User loggedInUser = authService.getLoggedInUser();
    String authenticationToken = authService.getAuthenticationToken();
    Source source = new Source("Import", String.valueOf(dealImportRequest.getJobId()), null);

    String ownerEmailId = (String) dealImportRequest.getDeal().get("ownerEmail");
    String createdByEmailId = (String) dealImportRequest.getDeal().get("createdByEmail");
    String updatedByEmailId = (String) dealImportRequest.getDeal().get("updatedByEmail");

    Optional<com.sling.sales.deal.domain.user.User> persistedLoggedInUser =
        userFacade.tryGetUserByIdAndTenantId(loggedInUser.getId(), loggedInUser.getTenantId());

    Mono<com.sling.sales.deal.domain.user.User> ownerByEmail = persistedLoggedInUser.map(Mono::just)
        .orElseGet(() -> userService.getUserDetails(loggedInUser.getId(), authenticationToken));

    Mono<com.sling.sales.deal.domain.user.User> importedBy = ownerByEmail;

    Mono<com.sling.sales.deal.domain.user.User> createdBy =
        getUserMono(authenticationToken, createdByEmailId, ownerByEmail, ownerEmailId).onErrorMap(
            e -> {
              String message = e.getMessage();
              log.error("Error while fetching User {}", message);
              return new DomainException(ErrorCode.CREATED_BY_USER_NOT_PRESENT);
            });
    Mono<com.sling.sales.deal.domain.user.User> updatedBy =
        ObjectUtils.isNotEmpty(createdByEmailId) && createdByEmailId.equals(updatedByEmailId) ? createdBy
            : getUserMono(authenticationToken, updatedByEmailId, ownerByEmail, ownerEmailId).onErrorMap(e -> {
              String message = e.getMessage();
              log.error("Error while fetching User {}", message);
              return new DomainException(ErrorCode.UPDATED_BY_USER_NOT_PRESENT);
            });

    if (ObjectUtils.isNotEmpty(ownerEmailId)) {
      ownerByEmail =
          getOwnerByEmail(ownerEmailId, authenticationToken).onErrorMap(
              e -> {
                String message = e.getMessage();
                log.error("Error while fetching User {}", message);
                return new DomainException(ErrorCode.USER_NOT_PRESENT);
              });
    }

    Mono<Optional<com.sling.sales.deal.common.dto.Money>> estimatedValue = getMoney((String) importRequest.get("estimatedValueCurrency")
        , (String) importRequest.get("estimatedValue"), authenticationToken);

    Mono<Optional<com.sling.sales.deal.common.dto.Money>> actualValue = getMoney((String) importRequest.get("actualValueCurrency")
        , (String) importRequest.get("actualValue"), authenticationToken);

    Mono<List<com.sling.sales.deal.common.dto.Contact>> associatedContacts = Mono.just(Collections.emptyList());

    if (ObjectUtils.isNotEmpty(dealImportRequest.getDeal().get("associatedContacts"))) {
      List<com.sling.sales.deal.common.dto.Contact> contacts1 = new ArrayList<>();

      for (Object contact : (List<Object>) dealImportRequest.getDeal().get("associatedContacts")) {
        contacts1.add(com.sling.sales.deal.common.dto.Contact.fromMap((Map<String, Object>) contact));
      }
      associatedContacts = Mono.just(contacts1);
    }

    List<Map<String, Object>> requestedProducts = (List<Map<String, Object>>) importRequest.getOrDefault("products", Collections.emptyList());

    Mono<List<com.sling.sales.deal.common.dto.Product>> products = fromProductRequestToProduct(requestedProducts, authenticationToken);

    Mono<Optional<com.sling.sales.deal.common.dto.Pipeline>> pipelineMono =
        fromPipelineRequestToPipeline((Map<String, Object>) importRequest.getOrDefault("pipeline", Collections.emptyMap()), authenticationToken);

    Mono<Optional<com.sling.sales.deal.common.dto.Company>> companyMono = Mono.just(Optional.empty());

    if (ObjectUtils.isNotEmpty(dealImportRequest.getDeal().get("associatedCompanies"))) {
      companyMono = Mono.just(Optional.of(com.sling.sales.deal.common.dto.Company
          .fromMap((Map<String, Object>) dealImportRequest.getDeal().get("associatedCompanies"))));
    }

    return
        Mono.zip(ownerByEmail, estimatedValue, actualValue, associatedContacts, products, pipelineMono, companyMono, importedBy)
            .map(tuple -> {
              DealRequest dealRequest = null;
              Date updatedAt = getFormattedDate(importRequest.get("updatedAt"), ErrorCode.INVALID_CREATED_AT_UPDATED_AT_DATE);
              Date createdAt = getFormattedDate(importRequest.get("createdAt"), ErrorCode.INVALID_CREATED_AT_UPDATED_AT_DATE);

              com.sling.sales.deal.common.dto.Company company = tuple.getT7().orElse(null);

              if (ObjectUtils.isNotEmpty(requestedProducts) && tuple.getT5().size() < requestedProducts.size()) {
                log.error("Invalid products {}", requestedProducts);
                throw new DomainException(ErrorCode.INVALID_PRODUCTS);
              }
              com.sling.sales.deal.common.dto.User owner = new com.sling.sales.deal.common.dto.User(tuple.getT1().getId(), tuple.getT1().getName());
              dealRequest = new DealRequest(
                  (String) importRequest.get("name"),
                  owner,
                  tuple.getT2().isPresent() ? tuple.getT2().get() : null,
                  tuple.getT3().isPresent() ? tuple.getT3().get() : null,
                  getFormattedDate(importRequest.get("estimatedClosureOn"), ErrorCode.INVALID_ACTUAL_CLOSURE_DATE),
                  getFormattedDate(importRequest.get("actualClosureDate"), ErrorCode.INVALID_ACTUAL_CLOSURE_DATE),
                  tuple.getT4(),
                  tuple.getT5(),
                  tuple.getT6().isPresent() ? tuple.getT6().get() : null,
                  (String) importRequest.get("pipelineStageReason"),
                  company,
                  getPickListValue((Map<String, Object>) importRequest.get("source")),
                  getPickListValue((Map<String, Object>) importRequest.get("campaign")),
                  (Map<String, Object>) importRequest.get("customFieldValues"),
                  (String) importRequest.get("subSource"),
                  (String) importRequest.get("utmSource"),
                  (String) importRequest.get("utmCampaign"),
                  (String) importRequest.get("utmMedium"),
                  (String) importRequest.get("utmContent"),
                  (String) importRequest.get("utmTerm"), null, createdAt, null, updatedAt).withImportedBy(
                  new com.sling.sales.deal.common.dto.User(tuple.getT8().getId(), tuple.getT8().getName()));
              dealRequest.validateCreatedAtUpdatedAt();
              log.info("=== dealRequest {}", dealRequest);
              return dealRequest;
            }).flatMap(dealRequest -> createDealViaImport(dealRequest, loggedInUser, authenticationToken, source, createdBy, updatedBy));
  }

  private Mono<User> getUserMono(String authenticationToken, String userEmailId, Mono<User> owner, String ownerEmailId) {
    if (ObjectUtils.isEmpty(userEmailId) || (ObjectUtils.isNotEmpty(ownerEmailId) && userEmailId.equals(ownerEmailId))) {
      return owner;
    }
    return getOwnerByEmail(userEmailId, authenticationToken);
  }

  private Deal createDeal(DealRequest dealToCreate, User loggedInUser, Source sourceMetaInfo,
      Tuple7<User, Set<Contact>, List<Product>, List<PipelineStage>, Optional<Company>, List<TenantCurrencyResponse>, Currency> responseTuple,
      User createdBy, User updatedBy, ExchangeRateResponse exchangeRateResponse, ExchangeRateResponse actualValueExchangeResponse,
      List<ProductFieldResponse> productFieldResponse) {

    HashMap<Long, Double> estimatedValueExchangeRate = exchangeRateResponse.getExchangeRate();
    HashMap<Long, Double> actualValueExchangeRate =
        actualValueExchangeResponse == null ? estimatedValueExchangeRate : actualValueExchangeResponse.getExchangeRate();
    Date date = dealToCreate.getActualClosureDate() == null ? new Date() : dealToCreate.getActualClosureDate();

    var loggedInUserDetails =
        userFacade.getExistingOrCreateNewUser(
            loggedInUser, loggedInUser.getTenantId());

    var creator = userFacade.getExistingOrCreateNewUser(createdBy, createdBy.getTenantId());
    var updator = userFacade.getExistingOrCreateNewUser(updatedBy, updatedBy.getTenantId());
    var owner =
        userFacade.getExistingOrCreateNewUser(
            responseTuple.getT1(), loggedInUser.getTenantId());
    var associatedContacts =
        responseTuple.getT2().stream()
            .map(contact -> contact.withTenantId(loggedInUser.getTenantId()))
            .map(this::getExistingOrCreateNewContact)
            .collect(Collectors.toCollection(LinkedHashSet::new));

    var estimatedValue =
        new ForexMoney(
            dealToCreate.getEstimatedValue().getCurrencyId(),
            dealToCreate.getEstimatedValue().getValue(), estimatedValueExchangeRate, new Date());

    var actualValue = dealToCreate.getActualValue() == null ? new ForexMoney(
        dealToCreate.getEstimatedValue().getCurrencyId(),
        dealToCreate.getEstimatedValue().getValue(), actualValueExchangeRate, new Date()) :
        new ForexMoney(
            dealToCreate.getActualValue().getCurrencyId(),
            dealToCreate.getActualValue().getValue(), actualValueExchangeRate, date);

    List<Product> productResponse = responseTuple.getT3();

    List<DealProduct> dealProducts = getDealProduct(dealToCreate, loggedInUser, productResponse, productFieldResponse);

    if (ObjectUtils.isNotEmpty(dealProducts)) {
      double estimatedValueAmount = dealProducts.stream()
          .mapToDouble(dealProduct -> dealProduct.getPrice().getAmount() * dealProduct.getQuantity()).sum();
      estimatedValue = new ForexMoney(estimatedValue.getCurrencyId(), estimatedValueAmount, exchangeRateResponse.getExchangeRate(), new Date());

    }
    var associatedCompany =
        getExistingOrCreateNewCompany(responseTuple.getT5(), loggedInUser.getTenantId());

    var source = dealToCreate.getSource() == null ? null
        : getSourceOrCampaign(dealToCreate.getSource(), loggedInUser.getTenantId());

    var campaign = dealToCreate.getCampaign() == null ? null
        : getSourceOrCampaign(dealToCreate.getCampaign(), loggedInUser.getTenantId());

    Set<DealUtm> dealUtmValues = getDealUtmValues(dealToCreate, creator);

    List<Field> customFields = fieldService.getCustomFields(loggedInUser);

    Deal aNew = Deal.createNew(
        dealToCreate.getName(),
        owner,
        estimatedValue,
        dealToCreate.getEstimatedClosureOn(),
        associatedContacts,
        loggedInUserDetails,
        associatedCompany,
        responseTuple.getT4(),
        source,
        campaign, dealToCreate.getCustomFieldValues(), customFields, dealProducts, responseTuple.getT7(), sourceMetaInfo, dealUtmValues, actualValue,
        responseTuple.getT6(), creator, updator, dealToCreate.getCreatedAt(), dealToCreate.getUpdatedAt(), exchangeRateResponse);

    if (ObjectUtils.isNotEmpty(dealToCreate.getImportedBy())) {
      aNew.setImportedBy(creator);
    }
    if (ObjectUtils.isNotEmpty(dealToCreate.getActualClosureDate())) {
      aNew.setActualClosureDate(dealToCreate.getActualClosureDate());
    }
    return aNew;
  }

  private List<DealProduct> getDealProduct(DealRequest dealToCreate, User loggedInUser, List<Product> t4,
      List<ProductFieldResponse> productFieldResponse) {

    if (ObjectUtils.isEmpty(dealToCreate.getProducts())) {
      return Collections.emptyList();
    }
    Map<Long, Product> productResponseMap = t4.stream().collect(toMap(Product::getId, product -> product));

    return dealToCreate.getProducts()
        .stream()
        .map(product -> {
          if (!productResponseMap.containsKey(product.getId())) {
            throw new InactiveProductException(ErrorCode.INVALID_PRODUCTS);
          }
          Product productResponse = productResponseMap.get(product.getId());
          Map<Long, ProductUnit> units = productResponse.getUnits()
              .stream()
              .collect(toMap(ProductUnit::getId, unit -> unit));
          if (!productResponse.isActive()) {
            throw new InactiveProductException(ErrorCode.INACTIVE_PRODUCT);
          }
          if (product.getUnits() != null && product.getUnits().getId() != null && !units.containsKey(product.getUnits().getId())) {
            throw new InactiveProductException(ErrorCode.INVALID_UNIT_ON_PRODUCT);
          }
          Money productPrice = getProductPrice(loggedInUser, product.getPrice(), productResponse.getPrice());

          if (!loggedInUser.canUpdateAllProduct() || !product.isOverrideProductProperty()) {
            return DealProduct
                .createNew(loggedInUser.getTenantId(), productResponse.getId(), productResponse.getName(), productPrice,
                    product.getQuantity(), product.getDiscount(), productResponse.getCategory(), productResponse.getHsnSacCode(),
                    productResponse.getCountryOfOrigin(),
                    productResponse.getCustomFieldValues(), product.getUnits() != null ? new Unit(units.get(product.getUnits().getId()).getId(),
                        units.get(product.getUnits().getId()).getDisplayName()) : null,
                    productFieldResponse, false, productResponse);
          }

          return DealProduct
              .createNew(loggedInUser.getTenantId(), productResponse.getId(), productResponse.getName(), productPrice, product.getQuantity(),
                  product.getDiscount(), product.getCategory(), product.getHsnSacCode(), product.getCountryOfOrigin(),
                  product.getCustomFieldValues(),
                  product.getUnits() != null ? new Unit(units.get(product.getUnits().getId()).getId(),
                      units.get(product.getUnits().getId()).getDisplayName()) : null,
                  productFieldResponse, false, productResponse);
        }).collect(toList());
  }


  private Deal saveNewDealAndRaiseEvents(Deal deal, User loggedInUser, List<TenantCurrencyResponse> tenantCurrencies) {
    Deal dealWithMetaInfo = dealRepository.saveAndFlush(deal);
    log.info("Deal created with id {} for tenant {}", dealWithMetaInfo.getId(), dealWithMetaInfo.getTenantId());

    CompletableFuture.runAsync(() -> {
        raiseDealCreatedEvent(dealWithMetaInfo);
        raiseDealAssociatedContactEvent(dealWithMetaInfo, loggedInUser.getId());
        raiseDealReassignedEvent(dealWithMetaInfo, loggedInUser.getId(), true);
        log.info("Deal created with id {} for tenant {} before returning", dealWithMetaInfo.getId(), dealWithMetaInfo.getTenantId());
        raiseDealCreatedEventV2(dealWithMetaInfo, loggedInUser.getTenantId(), loggedInUser.getId(), tenantCurrencies);
        log.info("Deal created v2 with id {} for tenant {} before returning", dealWithMetaInfo.getId(), dealWithMetaInfo.getTenantId());
    });
    return dealWithMetaInfo;
  }


  private Set<DealUtm> getDealUtmValues(DealRequest dealToCreate, User creator) {
    Set<DealUtm> dealUtms = new HashSet<>(emptySet());

    if (dealToCreate.getSubSource() != null || dealToCreate.getUtmSource() != null ||
        dealToCreate.getUtmCampaign() != null || dealToCreate.getUtmMedium() != null ||
        dealToCreate.getUtmContent() != null || dealToCreate.getUtmTerm() != null) {
      dealUtms.clear();
      dealUtms.add(new DealUtm(dealToCreate.getSubSource(), dealToCreate.getUtmSource(), dealToCreate.getUtmCampaign(), dealToCreate.getUtmMedium(),
          dealToCreate.getUtmContent(),
          dealToCreate.getUtmTerm(), creator));
    }
    return dealUtms;
  }

  private Money getProductPrice(User loggedInUser, com.sling.sales.deal.common.dto.Money requestedProductPrice, Money actualProductPrice) {
    if (loggedInUser.canUpdateAllProduct()) {
      return new Money(new com.sling.sales.deal.domain.deal.Currency(requestedProductPrice.getCurrencyId(), null), requestedProductPrice.getValue());
    }
    return actualProductPrice;
  }

  private void raiseDealCreatedEventV2(Deal deal, Long tenantId, Long userId, List<TenantCurrencyResponse> tenantCurrencies) {
    DealRequestV2 dealRequestV2 = toDealRequestV2(deal);
    dealRequestV2.populateCurrencyNameAndDisplayName(tenantCurrencies);
    DealEventPayloadV2 dealEventPayloadV2 = new DealEventPayloadV2(dealRequestV2, null,
        new Metadata(tenantId, userId, DEAL, deal.getId(), CREATED,
            null, emptySet(), null));
    dealEventPublisherV2.publishDealCreatedEventV2(dealEventPayloadV2);
  }

  public Deal getDealById(long dealId) {
    var loggedInUser = authService.getLoggedInUser();
    var permissionBasedQuerySpecification = getSpecificationsBasedOnReadPrivileges(loggedInUser);
    return tryGetExistingDealWithAllowedAction(loggedInUser, dealId, permissionBasedQuerySpecification);
  }

  public Deal getDealThatCanBeUpdatedByMeById(long dealId) {
    var loggedInUser = authService.getLoggedInUser();
    Specification<Deal> specificationsBasedOnUpdatePrivileges = getSpecificationsBasedOnUpdatePrivileges(loggedInUser);
    return tryGetExistingDealWithAllowedAction(loggedInUser, dealId, specificationsBasedOnUpdatePrivileges);
  }

  public Mono<List<PipelineStage>> getPipelineStagesForDeal(Long dealId) {
    var deal = getDealById(dealId);
    if (deal.getPipelineStages().isEmpty()) {
      throw new ResourceNotFoundException(ErrorCode.PIPELINE_NOT_ATTACHED);
    }
    Map<Long, PipelineStage> dealPipelineStageById = deal.getPipelineStages()
        .stream().collect(toMap(pipelineStage -> pipelineStage.getPipelineStageId(), pipelineStage -> pipelineStage));

    long pipelineId = dealPipelineStageById.values().stream().findFirst().get().getPipeline().getId();

    return pipelineService.getPipeline(pipelineId, authService.getAuthenticationToken(), authService.getTenantId())
        .map(pipelineResponse -> pipelineResponse.getStages().stream().map(pipelineStageResponse -> {
          PipelineStage pipelineStage = dealPipelineStageById.get(pipelineStageResponse.getId());
          return pipelineStage.with(pipelineStageResponse.getName(), pipelineStageResponse.getPosition(), pipelineStageResponse.getDescription(),
              pipelineStageResponse.getWinLikelihood());
        }).collect(toList()));
  }

  public Page<LookUp> lookUpDealsOwnedByMe(Optional<String> dealNameOption, Pageable page) {
    var loggedInUser = authService.getLoggedInUser();
    if (!loggedInUser.canLookUpDeals()) {
      throw new InsufficientPrivilegeException();
    }

    Specification<Deal> specification = belongToTenant(loggedInUser.getTenantId())
        .and(ownedBy(loggedInUser.getId()));

    return dealNameOption
        .map(partialDealName -> {
          Specification<Deal> withDealName = specification.and(containsName(partialDealName));
          return dealLookUpRepository.lookUpByName(withDealName, page);
        })
        .orElse(dealLookUpRepository.lookUpByName(specification, page));
  }

  public Page<LookUp> lookUpDealsThatCanBeUpdatedByMe(String partialDealName, Pageable page) {
    var loggedInUser = authService.getLoggedInUser();
    Specification<Deal> specificationsBasedOnUpdatePrivileges = getSpecificationsBasedOnUpdatePrivileges(loggedInUser);
    Specification<Deal> withName = specificationsBasedOnUpdatePrivileges.and(containsName(partialDealName));

    return dealLookUpRepository.lookUpByName(withName, page);
  }

  public Page<LookUp> lookUpDealsThatCanBeReadByMe(Optional<String> partialDealName, Pageable page) {
    var loggedInUser = authService.getLoggedInUser();
    Specification<Deal> specificationsBasedOnReadPrivileges = getSpecificationsBasedOnReadPrivileges(loggedInUser);
    return partialDealName
        .map(dealName -> {
          Specification<Deal> withName = specificationsBasedOnReadPrivileges.and(containsName(dealName));
          return dealLookUpRepository.lookUpByName(withName, page);
        })
        .orElse(dealLookUpRepository.lookUpByName(specificationsBasedOnReadPrivileges, page));
  }

  public Deal updateDeal(long dealId, DealRequest updatedDealDetails, Metadata metadata) {
    var loggedInUser = authService.getLoggedInUser();
    var permissionBasedQuerySpecification = getSpecificationsBasedOnUpdatePrivileges(loggedInUser);
    var existingDeal = tryGetExistingDeal(dealId, permissionBasedQuerySpecification);
    return updateDeal(existingDeal, updatedDealDetails, metadata, loggedInUser);
  }

  private void getUpdatedDealDetails(DealRequest updatedDealDetails) {
    if (ObjectUtils.isEmpty(updatedDealDetails.getProducts())) {
      updatedDealDetails.setProducts(emptyList());
    }
    if (ObjectUtils.isEmpty(updatedDealDetails.getCustomFieldValues())) {
      updatedDealDetails.setCustomFieldValues(emptyMap());
    }
    if (ObjectUtils.isEmpty(updatedDealDetails.getAssociatedContacts())) {
      updatedDealDetails.setAssociatedContacts(emptyList());
    }
  }

  public Deal patchDeal(long dealId, DealRequest updatedDealDetails, Metadata metadata) {
    var loggedInUser = authService.getLoggedInUser();
    if(metadata.getTenantId()==10450L){
      Optional<Deal> byId = dealRepository.findById(dealId);
      if(byId.isPresent()){
        log.info("Deal with id is present {} for 10450", byId.get().getId());
      }else {
        log.info("Deal with id is not present {} for 10450",dealId);
      }
    }
    var permissionBasedQuerySpecification = getSpecificationsBasedOnUpdatePrivileges(loggedInUser);
    var existingDeal = tryGetExistingDeal(dealId, permissionBasedQuerySpecification);
    addMultiValuePicklistValues(updatedDealDetails, existingDeal);
    return updateDeal(existingDeal, updatedDealDetails, metadata, loggedInUser);
  }

  private Deal updateDeal(Deal existingDeal, DealRequest updatedDealDetails, Metadata metadata, User loggedInUser) {
    DealRequest existingDealRequest = DealRequest.fromDeal(existingDeal);
    getUpdatedDealDetails(existingDealRequest);
    ForexMoney existingDealEstimatedValue = existingDeal.getEstimatedValue();
    ForexMoney existingDealActualValue = existingDeal.getActualValue();
    Integer olderVersion = existingDeal.getVersion();
    var authenticationToken = authService.getAuthenticationToken();
    var sourceMetaInfo = authService.getSource();
    Set<DealUtm> existingDealUtms = existingDeal.getDealUtms();
    DealRequestV2 existingDealRequestV2 = toDealRequestV2(existingDeal);
    var dealNameUpdated = isDealNameChanged(existingDeal.getName(), updatedDealDetails.getName());
    long oldOwnerId = existingDeal.getOwnedBy().getId();
    User loggedInUserDetails = userService.getUserDetails(loggedInUser.getId(), authenticationToken).block();

    Tuple7<User, Set<Contact>, List<Product>, List<PipelineStage>, Optional<Company>, List<TenantCurrencyResponse>, Currency> tuple7 = getDetailsOfDealPropertiesFromRequest(
        updatedDealDetails, loggedInUser, authenticationToken, existingDeal, metadata);

    ExchangeRateResponse currentDateExchangeRates = forexService.getCurrentDateExchangeRates(authenticationToken).block();
    List<ProductFieldResponse> productFieldResponse = getProductFieldResponse(authenticationToken, updatedDealDetails.getProducts(),
        loggedInUser.getTenantId()).block();
   /* return Mono.zip(getDetailsOfDealPropertiesFromRequest(
                updatedDealDetails, loggedInUser, authenticationToken, existingDeal, metadata), loggedInUserDetails,
            currentDateExchangeRates, productFieldResponse)
        .map(
            (responseTuple -> {*/
    var modifier =
        userFacade.getExistingOrCreateNewUser(
            loggedInUserDetails, loggedInUser.getTenantId());
    var owner =
        userFacade.getExistingOrCreateNewUser(
            tuple7.getT1(), loggedInUser.getTenantId());
    var associatedContactsSet = getContactsToUpdate(tuple7.getT2(), updatedDealDetails, existingDeal, updatedDealDetails.getRequestType()).stream()
        .map(contact -> contact.withTenantId(loggedInUser.getTenantId()))
        .map(this::getExistingOrCreateNewContact)
        .collect(toSet());

    if (metadata != null) {
      if (updatedDealDetails.getOperation() != FieldOperation.REPLACE) {
        var associatedContactIds = associatedContactsSet.stream().map(contact -> contact.getId()).collect(toSet());
        var filteredSet = existingDeal.getAssociatedContacts().stream().filter(contact -> !associatedContactIds.contains(contact.getId()))
            .collect(toSet());
        associatedContactsSet.addAll(filteredSet);
      }
    }

    var associatedContactsList = associatedContactsSet.stream().collect(Collectors.toCollection(LinkedHashSet::new));

    List<DealProduct> dealProducts = getDealProductsToUpdate(tuple7.getT3(), modifier, updatedDealDetails.getProducts(),
        existingDeal.getDealProducts(), loggedInUser, productFieldResponse, sourceMetaInfo);
    if (metadata != null && ObjectUtils.isEmpty(updatedDealDetails.getProducts())) {
      dealProducts = existingDeal.getDealProducts();
    }
    var estimatedValue =
        !isValidMoneyValue(updatedDealDetails.getEstimatedValue()) && metadata != null
            ? existingDealEstimatedValue :
            new ForexMoney(
                updatedDealDetails.getEstimatedValue().getCurrencyId(),
                updatedDealDetails.getEstimatedValue().getValue(), existingDealEstimatedValue.getExchangeRate(),
                existingDealEstimatedValue.getExchangeDate());

    var actualValue =
        isValidMoneyValue(updatedDealDetails.getActualValue())
            ? new ForexMoney(updatedDealDetails.getActualValue().getCurrencyId(), updatedDealDetails.getActualValue().getValue(),
            existingDealActualValue != null ? existingDealActualValue.getExchangeRate() : emptyMap(),
            existingDealActualValue != null ? existingDealActualValue.getExchangeDate() : null)
            : getActualValue(metadata, existingDeal);
    var associatedCompany =
        getExistingOrCreateNewCompany(tuple7.getT5(), loggedInUser.getTenantId());

    var name = isNull(updatedDealDetails.getName()) && metadata != null ? existingDeal.getName() : updatedDealDetails.getName();

    var estimatedClosureOn =
        isNull(updatedDealDetails.getEstimatedClosureOn()) && metadata != null ? existingDeal.getEstimatedClosureOn()
            : updatedDealDetails.getEstimatedClosureOn();

    var source = updatedDealDetails.getSource() == null ? getExistingPicklistOrNull(metadata, existingDeal.getSource())
        : getSourceOrCampaignToUpdate(updatedDealDetails.getSource(), existingDeal.getSource(), loggedInUser.getTenantId());

    var campaign = updatedDealDetails.getCampaign() == null ? getExistingPicklistOrNull(metadata, existingDeal.getCampaign())
        : getSourceOrCampaignToUpdate(updatedDealDetails.getCampaign(), existingDeal.getCampaign(), loggedInUser.getTenantId());

    var customFieldValues = getCustomFieldValues(updatedDealDetails, metadata, existingDeal);

    Set<DealUtm> dealUtmValues = getDealUtmValues(updatedDealDetails, modifier);

    Set<DealUtm> updatedUtm = new LinkedHashSet<>(dealUtmValues);

    if (ObjectUtils.isNotEmpty(existingDealUtms)) {
      updatedUtm = getUpdatedUtm(modifier, existingDealUtms, dealUtmValues, updatedDealDetails);
    }
    var actualClosureDate = getActualClosureDate(updatedDealDetails.getActualClosureDate(), existingDeal, metadata);

    List<Field> customFields = fieldService.getCustomFields(loggedInUser);

    Deal dealToUpdate = existingDeal.update(
        name,
        owner,
        estimatedValue,
        actualValue,
        estimatedClosureOn,
        actualClosureDate,
        associatedContactsList,
        associatedCompany,
        modifier,
        tuple7.getT4(),
        source,
        campaign,
        customFieldValues,
        customFields, dealProducts, tuple7.getT7(), sourceMetaInfo, updatedUtm, tuple7.getT6(),
        existingDeal.getDealProducts(), currentDateExchangeRates);
    Deal updatedDeal = dealRepository.saveAndFlush(dealToUpdate);
//              return Tuples.of(updatedDeal, tuple7.getT6());
            /*}))
        .map(
            tuples -> {*/
    if (dealNameUpdated) {
      log.info("DealName updated event raised for dealId {} oldName {} and newName {}", updatedDeal.getId(),
          existingDeal.getName(),
          updatedDeal.getName());
      raiseDealNameUpdatedEvent(updatedDeal);
    }
//              return tuples;
          /*  })
        .map(
            tuples -> {*/
    Deal deal = updatedDeal;
    DealRequest dealRequest = DealRequest.fromDeal(deal);
    getUpdatedDealDetails(dealRequest);
    if (dealRequest.equals(existingDealRequest)) {
      log.info("Old and New Deal requests are same in facade returning for deal:{} and tenant:{}", deal.getId(), loggedInUser.getTenantId());
      Optional<Action> sharedAction = dealSharingFacade.getGrantedActionForUser(loggedInUser, existingDeal.getId(), existingDeal.getOwnedBy());
      deal.setAllowedActionsForUser(loggedInUser, sharedAction);
      deal.setVersion(olderVersion);
      return deal;
    }
    log.debug("Old and New Deal requests are different in facade for deal:{} and tenant:{}", deal.getId(), loggedInUser.getTenantId());
    if (deal.getVersion() > olderVersion) {
      if (deal.getOwnedBy().getId() != existingDealRequestV2.getOwnedBy().getId()) {
        raiseDealReassignedEvent(deal, oldOwnerId, true);
      }
      raiseDealUpdatedEvent(deal);
      DealRequestV2 newDealRequestV2 = toDealRequestV2(deal);
      raiseDealUpdatedEventV2(newDealRequestV2, existingDealRequestV2, metadata, UPDATED, loggedInUser,
          metadata == null || metadata.isExecuteWorkflow(),
          metadata == null || metadata.isSendNotification(), tuple7.getT6());
      raiseDealAssociatedContactEvent(deal, loggedInUser.getId());
    }
    Optional<Action> sharedAction = dealSharingFacade.getGrantedActionForUser(loggedInUser, deal.getId(), deal.getOwnedBy());
    return deal.setAllowedActionsForUser(loggedInUser, sharedAction);
//            });
  }

  private boolean isDealNameChanged(String existingName, String updatableName) {
    if (ObjectUtils.isEmpty(updatableName)) {
      return false;
    }
    return !existingName.equals(updatableName);
  }

  private void addMultiValuePicklistValues(DealRequest updatedDealDetails, Deal existingDeal) {
    if (ObjectUtils.isEmpty(existingDeal.getCustomFieldValues())) {
      return;
    }

    Map<String, Object> updateRequestMultiPicklistValues = ObjectUtils.isEmpty(updatedDealDetails.getCustomFieldValues())
        ? new HashMap<>()
        : updatedDealDetails.getCustomFieldValues().entrySet()
            .stream().filter(entry -> entry.getValue() instanceof List)
            .collect(toMap(Entry::getKey, Entry::getValue));
    Map<String, Object> existingMultiPicklistValues = existingDeal.getCustomFieldValues().entrySet()
        .stream().filter(entry -> entry.getValue() instanceof List)
        .collect(toMap(Entry::getKey, Entry::getValue));
    updateRequestMultiPicklistValues.entrySet().forEach(entry -> {
      var updatedValues = addMultipickListValues(entry.getValue(), existingMultiPicklistValues.get(entry.getKey()));
      updatedDealDetails.getCustomFieldValues().put(entry.getKey(), updatedValues);
    });
  }

  private List<Map<String, Object>> addMultipickListValues(Object newValue, Object oldValue) {
    List<Map<String, Object>> newValueList = new ObjectMapper().convertValue(newValue, new TypeReference<List<Map<String, Object>>>() {
      @Override
      public java.lang.reflect.Type getType() {
        return super.getType();
      }
    });
    List<Map<String, Object>> existingValueList = new ObjectMapper().convertValue(oldValue, new TypeReference<List<Map<String, Object>>>() {
      @Override
      public java.lang.reflect.Type getType() {
        return super.getType();
      }
    });
    if (ObjectUtils.isEmpty(existingValueList)) {
      return newValueList;
    }
    if (ObjectUtils.isEmpty(newValueList)) {
      return existingValueList;
    }
    existingValueList.addAll(newValueList);
    return existingValueList.stream().distinct().collect(toList());
  }

  private Date getActualClosureDate(Date actualClosureDate, Deal existingDeal, Metadata metadata) {
    Optional<PipelineStage> currentStage = existingDeal.getCurrentStage();
    if (currentStage.isPresent() && currentStage.get().getType().equals(StageTypes.OPEN)) {
      return null;
    }
    if (metadata != null && actualClosureDate == null) {
      return existingDeal.getActualClosureDate();
    }
    return actualClosureDate;
  }

  private Set<DealUtm> getUpdatedUtm(User modifier, Set<DealUtm> existingDealUtms, Set<DealUtm> requestedDealUtms, DealRequest dealRequest) {
    Set<DealUtm> utms = new LinkedHashSet<>();
    DealUtm persistedDealUtm = existingDealUtms.iterator().next();
    Iterator<DealUtm> iterator = requestedDealUtms.iterator();
    DealUtm requestedDealUtm = iterator.hasNext() ? iterator.next() : new DealUtm();

    if (ObjectUtils.notEqual(persistedDealUtm.getUtmCampaign(), requestedDealUtm.getUtmCampaign()) ||
        ObjectUtils.notEqual(persistedDealUtm.getUtmContent(), requestedDealUtm.getUtmContent()) ||
        ObjectUtils.notEqual(persistedDealUtm.getUtmMedium(), requestedDealUtm.getUtmMedium()) ||
        ObjectUtils.notEqual(persistedDealUtm.getSubSource(), requestedDealUtm.getSubSource()) ||
        ObjectUtils.notEqual(persistedDealUtm.getUtmTerm(), requestedDealUtm.getUtmTerm()) ||
        ObjectUtils.notEqual(persistedDealUtm.getUtmSource(), requestedDealUtm.getUtmSource())) {
      requestedDealUtm.setUpdatedBy(modifier);
      persistedDealUtm.update(requestedDealUtm, dealRequest);
      utms.add(persistedDealUtm);
    } else {
      utms.add(persistedDealUtm);
    }
    return utms;
  }

  private List<DealProduct> getDealProductsToUpdate(
      List<Product> products, User modifier, List<com.sling.sales.deal.common.dto.Product> dealProductRequests,
      List<DealProduct> existingDealProducts, User loggedInUser, List<ProductFieldResponse> productFieldResponse,
      Source sourceMetaInfo) {

    if (ObjectUtils.isEmpty(dealProductRequests)) {
      return Collections.emptyList();
    }
    final Map<Long, DealProduct> existingDealProductMap = new HashMap<>();
    if (ObjectUtils.isNotEmpty(existingDealProducts)) {
      existingDealProducts.stream()
          .forEach(dealProduct -> {
            existingDealProductMap.put(dealProduct.getId(), dealProduct);
          });
    }
    Map<Long, Product> productResponseMap = products.stream().collect(Collectors.toMap(product -> product.getId(), product -> product));

    List<Long> existingProductRequestList = dealProductRequests.stream()
        .filter(product -> product.getDealProductId() != null)
        .map(product -> product.getDealProductId())
        .collect(toList());
    Set<Long> existingProductRequestSet = new HashSet<>(existingProductRequestList);
    if (existingProductRequestList.size() != existingProductRequestSet.size()) {
      throw new DomainException(ErrorCode.INVALID_PRODUCTS);
    }
    return dealProductRequests
        .stream()
        .map(productRequest -> {
          if (productRequest.getDealProductId() != null) {
            DealProduct existingDealProduct = existingDealProductMap.get(productRequest.getDealProductId());
            if (ObjectUtils.isEmpty(existingDealProduct)) {
              throw new DomainException(ErrorCode.INVALID_PRODUCTS);
            }
            Money productPriceToCreateDeal = getProductPrice(loggedInUser, productRequest.getPrice(), existingDealProduct.getPrice());
            if (!loggedInUser.canUpdateAllProduct() || !productRequest.isOverrideProductProperty()) {
              return existingDealProduct.
                  withQuantityAndDiscountAndUnit(productRequest.getQuantity(), productRequest.getDiscount(), productRequest.getUnits());
            }
            DealProduct dealProduct = existingDealProduct
                .update(productRequest.getQuantity(), productRequest.getDiscount(), productRequest.getCategory(),
                    productRequest.getCountryOfOrigin(), productRequest.getHsnSacCode(),
                    productPriceToCreateDeal, productRequest.getUnits(), productRequest.getCustomFieldValues(), productFieldResponse);
            return dealProduct;
          } else {
            if (!productResponseMap.containsKey(productRequest.getId())) {
              throw new DomainException(ErrorCode.INVALID_PRODUCTS);
            }
            Product productResponse = productResponseMap.get(productRequest.getId());
            if (!productResponse.isActive()) {
              throw new InactiveProductException(ErrorCode.INACTIVE_PRODUCT);
            }
            Map<Long, ProductUnit> units = productResponse.getUnits()
                .stream()
                .collect(toMap(ProductUnit::getId, unit -> unit));
            if (productRequest.getUnits() != null && productRequest.getUnits().getId() != null && !units.containsKey(
                productRequest.getUnits().getId())) {
              throw new InactiveProductException(ErrorCode.INVALID_UNIT_ON_PRODUCT);
            }
            Money productPrice = getProductPrice(loggedInUser, productRequest.getPrice(), productResponse.getPrice());
            boolean viaWorkflow = false;
            if (!Objects.isNull(sourceMetaInfo)) {
              viaWorkflow = sourceMetaInfo.getType().equalsIgnoreCase("Workflow") ? true : false;
            }

            if (!loggedInUser.canUpdateAllProduct() || !productRequest.isOverrideProductProperty()) {
              return DealProduct
                  .createNew(modifier.getTenantId(), productResponse.getId(), productResponse.getName(), productPrice,
                      productRequest.getQuantity(), productRequest.getDiscount(), productResponse.getCategory(), productResponse.getHsnSacCode(),
                      productResponse.getCountryOfOrigin(), productResponse.getCustomFieldValues(),
                      productRequest.getUnits() != null ? new Unit(units.get(productRequest.getUnits().getId()).getId(),
                          units.get(productRequest.getUnits().getId()).getDisplayName()) : null,
                      productFieldResponse, viaWorkflow, productResponse);
            }
            return DealProduct
                .createNew(modifier.getTenantId(), productRequest.getId(), productResponse.getName(), productPrice,
                    productRequest.getQuantity(), productRequest.getDiscount(), productRequest.getCategory(),
                    productRequest.getHsnSacCode(), productRequest.getCountryOfOrigin(),
                    productRequest.getCustomFieldValues(),
                    productRequest.getUnits() != null ? new Unit(units.get(productRequest.getUnits().getId()).getId(),
                        units.get(productRequest.getUnits().getId()).getDisplayName()) : null,
                    productFieldResponse, viaWorkflow, productResponse);
          }
        }).collect(toList());
  }

  private Map<String, Object> getCustomFieldValues(DealRequest updatedDealDetails, Metadata metadata, Deal existingDeal) {

    if (metadata == null) {
      return updatedDealDetails.getCustomFieldValues();
    }
    if (updatedDealDetails.getCustomFieldValues() == null) {
      return existingDeal.getCustomFieldValues();
    }
    return mergeExistingAndNewCustomFieldValues(updatedDealDetails.getCustomFieldValues(), existingDeal.getCustomFieldValues());
  }

  private Map<String, Object> mergeExistingAndNewCustomFieldValues(Map<String, Object> newCustomFieldValues,
      Map<String, Object> existingCustomFieldValues) {
    Map<String, Object> mergedCustomFieldValues = new HashMap<>();
    mergedCustomFieldValues.putAll(Optional.ofNullable(existingCustomFieldValues).orElse(Collections.emptyMap()));
    mergedCustomFieldValues.putAll(Optional.ofNullable(newCustomFieldValues).orElse(Collections.emptyMap()));
    return mergedCustomFieldValues;
  }

  private PicklistValue getExistingPicklistOrNull(Metadata metadata, PicklistValue existingPicklistValue) {
    if (metadata == null) {
      return null;
    }
    return existingPicklistValue;
  }

  private PicklistValue getSourceOrCampaign(com.sling.sales.deal.domain.field.dto.PicklistValue requestedPicklistValue, long tenantId) {

    PicklistValue picklistValue = picklistValueFacade.getPicklistValueByIdAndTenantId(requestedPicklistValue.getId(), tenantId);
    if (picklistValue.isDisabled()) {
      throw new InvalidPicklistValueException(ErrorCode.INVALID_PICKLIST_VALUE);
    }
    return picklistValue;
  }

  private PicklistValue getSourceOrCampaignToUpdate(com.sling.sales.deal.domain.field.dto.PicklistValue requestedPicklistValue,
      PicklistValue existingPicklistValue, long tenantId) {
    PicklistValue picklistValue = picklistValueFacade.getPicklistValueByIdAndTenantId(requestedPicklistValue.getId(), tenantId);

    if (ObjectUtils.isEmpty(existingPicklistValue) && picklistValue.isDisabled()) {
      throw new InvalidPicklistValueException(ErrorCode.INVALID_PICKLIST_VALUE);
    }
    if (ObjectUtils.isNotEmpty(existingPicklistValue) && existingPicklistValue.getId().equals(picklistValue.getId())) {
      return picklistValue;
    }
    if (picklistValue.isDisabled()) {
      throw new InvalidPicklistValueException(ErrorCode.INVALID_PICKLIST_VALUE);
    }
    return picklistValue;
  }

  public ForexMoney getActualValue(Metadata metadata, Deal existindDeal) {
    ForexMoney existingDealActualValue = existindDeal.getActualValue();
    Map<Long, Double> map = existingDealActualValue != null ? existingDealActualValue.getExchangeRate() : emptyMap();
    Date date = existingDealActualValue != null ? existingDealActualValue.getExchangeDate() : null;
    return metadata == null ? new ForexMoney(null, null, map, date) : existindDeal.getActualValue();
  }

  private void raiseDealUpdatedEventV2(DealRequestV2 newDealRequestV2, DealRequestV2 existingDealRequestV2, Metadata metadata,
      Metadata.Action action, User loggeInUser, boolean executeWorkflow, boolean sendNotification, List<TenantCurrencyResponse> tenantCurrencies) {
    DealEventPayloadV2 dealEventPayloadV2 = null;

    newDealRequestV2.populateCurrencyNameAndDisplayName(tenantCurrencies);
    existingDealRequestV2.populateCurrencyNameAndDisplayName(tenantCurrencies);

    if (metadata == null) {
      Metadata newMetadata = new Metadata(loggeInUser.getTenantId(), loggeInUser.getId(), DEAL,
          existingDealRequestV2.getId(), action, null,
          emptySet(), null).withExecuteWorkflow(executeWorkflow).withSendNotification(sendNotification);
      log.info("passing new metadata generated from front end update request for deal {} with metadata {}", newMetadata.getEntityId(), newMetadata);
      dealEventPayloadV2 = new DealEventPayloadV2(newDealRequestV2, existingDealRequestV2, newMetadata);
    }

    if (metadata != null) {
      Metadata updatedMetadata = metadata.withEntityAction(action).withSendNotification(metadata.isSendNotification())
          .withExecuteWorkflow(metadata.isExecuteWorkflow());
      log.info("passing existing metadata with updated action received from workflow service update request for deal {} with metadata {}",
          updatedMetadata.getEntityId(), updatedMetadata);
      dealEventPayloadV2 = new DealEventPayloadV2(newDealRequestV2, existingDealRequestV2, updatedMetadata);
    }

    dealEventPublisherV2.publishDealUpdatedEventV2(dealEventPayloadV2);
  }

  private Set<Contact> getContactsToUpdate(Set<Contact> readableContacts, DealRequest updateRequest, Deal existingDeal, RequestType requestType) {
    if (requestType.equals(JSON_PATCH)) {
      return new LinkedHashSet<>(readableContacts);
    }

    var readableContactIds =
        readableContacts.stream().map(Contact::getId).collect(Collectors.toSet());
    var updateRequestContactIds =
        updateRequest.getAssociatedContacts().stream().map(contact -> contact.getId()).collect(Collectors.toSet());

    if (updateRequest.getOperation() == FieldOperation.REPLACE) {
      return new LinkedHashSet<>(readableContacts);
    }

    Set<Contact> existingNonReadableContacts =
        existingDeal.getAssociatedContacts().stream()
            .filter(contact -> !readableContactIds.contains(contact.getId()))
            .collect(Collectors.toCollection(LinkedHashSet::new));

    Set<Contact> requestedReadableContacts =
        readableContacts.stream()
            .filter(contact -> updateRequestContactIds.contains(contact.getId()))
            .collect(Collectors.toCollection(LinkedHashSet::new));
    return
        Stream
            .of(existingNonReadableContacts, requestedReadableContacts)
            .flatMap(Collection::stream)
            .collect(Collectors.toCollection(LinkedHashSet::new));
  }

  private void raiseDealAssociatedContactEvent(Deal deal, Long userId) {
    DealAssociatedContactEvent event = new DealAssociatedContactEvent(
        deal.getId(),
        deal.getTenantId(),
        userId,
        deal.getAssociatedContacts()
            .stream()
            .map(Contact::getId)
            .collect(toUnmodifiableList())
    );
    dealEventPublisher.publishDealAssociatedContactEvent(event);
  }

  @Transactional
  public void updateAssociatedContact(ContactNameUpdatedEvent contactNameUpdatedEvent) {
    contactRepository
        .findContactByIdAndTenantId(
            contactNameUpdatedEvent.getContactId(), contactNameUpdatedEvent.getTenantId())
        .map(
            existingContact ->
                existingContact.withName(
                    contactNameUpdatedEvent.getFirstName(), contactNameUpdatedEvent.getLastName()))
        .map(contactRepository::saveAndFlush)
        .map(
            updatedContact -> {
              var dealsToUpdate = dealRepository.findAll(
                  isAssociatedWithContact(updatedContact).and(belongToTenant(contactNameUpdatedEvent.getTenantId())));
              dealsToUpdate.forEach(
                  deal -> {
                    dealRepository.updateVersion(deal.getId(), deal.getVersion());
                    var updatedDeal =
                        dealRepository
                            .findOne(
                                belongToTenant(deal.getTenantId()).and(withDealId(deal.getId())))
                            .get();
                    raiseDealUpdatedEvent(updatedDeal);
                  });
              return updatedContact;
            });
  }

  public void updateDealVersion(UserNameUpdatedEvent userNameUpdated) {
    log.info("Updating Deal version for ownedBy:{} and tenantId:{}", userNameUpdated.getUserId(), userNameUpdated.getTenantId());
    dealRepository.updateDealVersion(userNameUpdated.getTenantId(), userNameUpdated.getUserId());
  }

  @Transactional
  public void updateAssociatedUser(UserNameUpdatedEvent event) {
    userFacade.tryUpdateUser(event.getUserId(),event.getTenantId(),event.getFirstName(), event.getLastName())
        .ifPresentOrElse(user -> {
          if(dealRepository.isUserIdPresentInField(user.getTenantId(), user.getId())){
            updateDealVersion(event);
            raiseUserNameUpdatedEvent(event);
          }
        },() -> log.info("User with Id {} and tenantId {} does not exist for user.name.updated event", event.getUserId(), event.getTenantId()));
  }
  private void raiseUserNameUpdatedEvent(UserNameUpdatedEvent userNameUpdatedEvent) {
    dealEventPublisher.publishUserNameUpdated(userNameUpdatedEvent);
  }
  @Transactional
  public void updateAssociatedPipeline(PipelineUpdatedEvent event) {
    log.info("Deal pipeline event processing started tenantId {} and pipelineId {} ", event.getTenantId(), event.getId());
    dealPipelineFacade
        .updatePipeline(
            event.getId(),
            event.getTenantId(),
            event.getName(),
            event.getUnqualifiedReasons(),
            event.getLostReasons())
        .map(
            pipeline -> {
              dealRepository
                  .findAll(associatedWithPipelineId(event.getId()))
                  .forEach(
                      deal -> {
                        List<PipelineStage> updatedStages =
                            event.getStages().stream()
                                .map(
                                    stage ->
                                        dealPipelineFacade.getOrCreatePipelineStage(
                                            pipeline, stage, deal.getId()))
                                .sorted(Comparator.comparing(PipelineStage::getPosition))
                                .collect(toList());
                        Deal updatedDeal =
                            dealRepository.saveAndFlush(deal.withPipelineStages(updatedStages, null, deal.getUpdatedAt()));
                        dealRepository.updateVersion(deal.getId(), deal.getVersion());
                        raiseDealUpdatedEvent(updatedDeal);
                      });
              return pipeline;
            });
    log.info("Deal pipeline event processing ended for tenantId {} and pipelineId {} ", event.getTenantId(), event.getId());
  }

  @Transactional
  public int updateAssociatedPipeline(PipelineUpdatedEventV2 event) {
    PipelinePayload entity = event.getEntity();
    log.info("Deal pipeline event v2 processing started tenantId {} and pipelineId {} ", entity.getTenantId(), entity.getId());

    Map<Long, PipelineStagePayload> entityPipelineStageById = entity.getStages().stream()
        .collect(toMap(pipelineStagePayload -> pipelineStagePayload.getId(), pipelineStagePayload -> pipelineStagePayload));

    Map<Long, PipelineStagePayload> oldEntityPipelineStageById = event.getOldEntity().getStages().stream()
        .collect(toMap(pipelineStagePayload -> pipelineStagePayload.getId(), pipelineStagePayload -> pipelineStagePayload));

    if (entityPipelineStageById.size() != oldEntityPipelineStageById.size()) {
      List<PipelineStagePayload> newStages = entityPipelineStageById.entrySet().stream()
          .filter(longPipelineStagePayloadEntry -> !oldEntityPipelineStageById.containsKey(longPipelineStagePayloadEntry.getKey()))
          .map(longPipelineStagePayloadEntry -> longPipelineStagePayloadEntry.getValue())
          .collect(toList());
      log.info("Deal pipeline event v2 {} new stages added", newStages.size());
      addNewStageOnDeal(newStages, entity.getId());
    }
    List<PipelineStagePayload> pipelineStageChanges = oldEntityPipelineStageById
        .entrySet()
        .stream()
        .filter(longPipelineStagePayloadEntry -> longPipelineStagePayloadEntry.getValue()
            .isChange(entityPipelineStageById.get(longPipelineStagePayloadEntry.getKey())))
        .map(longPipelineStagePayloadEntry -> entityPipelineStageById.get(longPipelineStagePayloadEntry.getKey()))
        .collect(toList());
    updatePipelineStageDetails(entityPipelineStageById, oldEntityPipelineStageById, entity.getId(), entity.getTenantId(), entity.getUpdatedBy());

    log.info("Deal pipeline event v2 processing ended for tenantId {} and pipelineId {} ", entity.getTenantId(), entity.getId());
    return 0;
  }

  private void updatePipelineStageDetails(Map<Long, PipelineStagePayload> entityPipelineStageById,
      Map<Long, PipelineStagePayload> oldEntityPipelineStageById, long pipelineId, long tenantId, long updatedBy) {
    dealPipelineFacade.updatePipelineStageDetail(entityPipelineStageById, oldEntityPipelineStageById, pipelineId, tenantId, updatedBy);
  }

  private int addNewStageOnDeal(List<PipelineStagePayload> newStages, long pipelineId) {
    return dealPipelineFacade.insertNewStage(newStages, pipelineId);
  }

  public List<Long> getDealIdsForProduct(long productId) {
    return dealRepository.findAll(associatedWithProduct(productId)).stream()
        .map(deal -> deal.getId())
        .collect(toList());
  }

  @Transactional
  public void updateAssociatedProduct(ProductNameUpdatedEvent event) {
    dealProductRepository.updateProductNameByProductIdAndTenantId(event.getProductId(), event.getTenantId(), event.getProductName());
    dealRepository.updateVersionByProductId(event.getProductId());
    dealProductRepository.findAllByProductIdAndTenantId(event.getProductId(), event.getTenantId())
        .stream()
        .forEach(dealProduct -> {
          raiseDealUpdatedEvent(dealProduct.getDeal());
        });
  }

  @Transactional
  public PicklistValue updatePicklistValueDisplayName(com.sling.sales.deal.domain.field.dto.PicklistValue pickListValueRequest,
      Long picklistValueId) {

    PicklistValue updatedPicklistValue = picklistValueFacade.updateDisplayName(pickListValueRequest, picklistValueId);
    Field field = updatedPicklistValue.getPicklist().getField();
    if (!field.isStandard() && field.getFieldType().equals(FieldType.PICK_LIST)) {
      String picklistName = "'{" + field.getName() + ", name}'";
      String displayName = "'\"" + updatedPicklistValue.getDisplayName() + "\"'";
      String condition = "custom_field_values->'" + field.getName() + "'->" + "'id'";
      String query =
          "update deal set version = version+1, custom_field_values = jsonb_set(custom_field_values," + picklistName + "," + displayName + ") where "
              + condition + "='" + updatedPicklistValue.getId() + "'";
      entityManager.createNativeQuery(query)
          .executeUpdate();
      entityManager.close();
      return updatedPicklistValue;
    }

    if (!field.isStandard() && field.getFieldType().equals(FieldType.MULTI_PICKLIST)) {
      StringBuilder sb = new StringBuilder().append(
              "with sub as (SELECT CAST (id AS INTEGER) as dealId,CAST ( sub.index-1 AS INTEGER) as mindex FROM deal,"
                  + "LATERAL jsonb_array_elements(custom_field_values->'").append(field.getName())
          .append("') WITH ORDINALITY AS sub(value, index) WHERE sub.value->>'id' = '")
          .append(picklistValueId).append("'").append("AND tenant_id=").append(field.getTenantId())
          .append(" )UPDATE deal SET  version=version+1,custom_field_values =jsonb_set(custom_field_values,CAST(concat('{")
          .append(field.getName())
          .append(",',(sub.mindex),',name}') as TEXT[]),'\"").append(updatedPicklistValue.getDisplayName())
          .append("\"',false) from sub WHERE id=sub.dealId AND tenant_id=").append(field.getTenantId());
      entityManager.createNativeQuery(sb.toString()).executeUpdate();
      entityManager.close();
      return updatedPicklistValue;
    }
    updateDealVersionForStandardPicklistValue(field, updatedPicklistValue);
    return updatedPicklistValue;
  }

  private void updateDealVersionForStandardPicklistValue(Field field, PicklistValue updatedPicklistValue) {
    if (field.getName().equals("source")) {
      dealRepository.updateVersionBySourcePicklistValueId(updatedPicklistValue.getId());
      return;
    }
    dealRepository.updateVersionByCampaignPicklistValueId(updatedPicklistValue.getId());
  }

  public void updateDealHavingCategoryOnProduct(PicklistValueUpdateEventDetail picklistValueDetail) {
    if (picklistValueDetail.getFieldName().equals("category")) {
      dealProductRepository.updateDealWithCategoryOnProductByCategoryId(picklistValueDetail.getId(), picklistValueDetail.getDisplayName());
    }
  }

  @Transactional
  public void updateAssociatedCompany(CompanyNameUpdatedEvent companyNameUpdatedEvent) {
    companyRepository
        .findByIdAndTenantId(
            companyNameUpdatedEvent.getCompanyId(), companyNameUpdatedEvent.getTenantId())
        .map(existingCompany -> existingCompany.withName(companyNameUpdatedEvent.getCompanyName()))
        .map(companyRepository::saveAndFlush)
        .map(
            updatedCompany -> {
              var dealsToUpdate =
                  dealRepository.findAll(isAssociatedWithCompany(updatedCompany.getId()));
              dealsToUpdate.forEach(
                  deal -> {
                    dealRepository.updateVersion(deal.getId(), deal.getVersion());
                    var updatedDeal =
                        dealRepository
                            .findOne(
                                belongToTenant(deal.getTenantId()).and(withDealId(deal.getId())))
                            .get();
                    raiseDealUpdatedEvent(updatedDeal);
                  });
              return updatedCompany;
            });
  }

  @Transactional
  public Mono<List<PipelineStage>> activateStage(
      Long dealId, Long stageIdToActivate, StageActivationRequest activationRequest) {

    var authenticationToken = authService.getAuthenticationToken();

    var loggedInUser = authService.getLoggedInUser();

    var sourceMetaInfo = authService.getSource();
    var permissionBasedQuerySpecification = getSpecificationsBasedOnUpdatePrivileges(loggedInUser);
    Deal deal =
        tryGetExistingDealWithAllowedAction(loggedInUser, dealId, permissionBasedQuerySpecification);

    if (deal.getPipelineStages().isEmpty()) {
      throw new ResourceNotFoundException(ErrorCode.PIPELINE_NOT_ATTACHED);
    }

    PipelineStage pipelineStage = deal.getPipelineStages().stream()
        .filter(stage -> stage.getPipelineStageId() == stageIdToActivate)
        .findFirst()
        .orElseThrow(() -> new ResourceNotFoundException(ErrorCode.PIPELINE_STAGE_NOT_FOUND));

    if (!OPEN.equals(pipelineStage.getType())) {
      activationRequest.setActualClosureDate(activationRequest.getActualClosureDate() == null
          ? new Date()
          : activationRequest.getActualClosureDate());
      log.info("Actual closure date setting in activation request:{}", activationRequest.getActualClosureDate());
    }

    if (CLOSED_WON.equals(pipelineStage.getType())) {
      if (ObjectUtils.isEmpty(deal.getActualValue()) && ObjectUtils.isEmpty(
          activationRequest.getActualValue())) {
        throw new InvalidPipelineException(ErrorCode.ACTUAL_VALUE_NOT_PRESENT);
      }

      activationRequest.setEstimatedValue(activationRequest.getEstimatedValue() == null
          ? new com.sling.sales.deal.common.dto.Money(deal.getEstimatedValue().getCurrencyId(), deal.getEstimatedValue().getAmount())
          : activationRequest.getEstimatedValue());
      log.info("actual value after setting in activation request:{} and currency:{}", activationRequest.getActualValue().getValue(),
          activationRequest.getActualValue().getCurrencyId());
      log.info("estimation value after setting in activation request:{} and currency:{}", activationRequest.getEstimatedValue().getValue(),
          activationRequest.getEstimatedValue().getCurrencyId());
      activationRequest.validateEstimatedAndActualValueCurrency(activationRequest.getActualValue(), activationRequest.getEstimatedValue());
    }
    Mono<User> persistedUser = createUserIfNotExist(loggedInUser, authenticationToken);
    DealRequestV2 oldDealRequestV2 = toDealRequestV2(deal);

    List<PipelineStage> pipelineStages =
        dealPipelineFacade.updateStages(
            deal.getPipelineStages(),
            stageIdToActivate,
            activationRequest.getReasonForClosing()
        );
    var metaInfo = deal.getMetaInfo();

    var tenantCurrencyMono = userService.getTenant(authenticationToken)
        .flatMap(tenant -> currencyService.getCurrencyByName(tenant.getCurrency(), authenticationToken));

    var exchangeRateByDate = activationRequest.getActualClosureDate() == null ? forexService.getCurrentDateExchangeRates(authenticationToken) :
        forexService.getExchangeRateByDate(authenticationToken, activationRequest.getActualClosureDate());
    Mono<List<Product>> productDetails = getProductDetails(authenticationToken, activationRequest.getProducts());
    var tenantCurrencies = currencyService.getTenantCurrencies(authenticationToken);

    Mono<PipelineResponse> pipeline = pipelineService.getPipeline(pipelineStages.get(0).getPipeline().getId(), authenticationToken,
        loggedInUser.getTenantId());

    Mono<List<ProductFieldResponse>> productFieldResponse = getProductFieldResponse(authenticationToken, activationRequest.getProducts(),
        loggedInUser.getTenantId());

    return Mono.zip(productDetails, tenantCurrencyMono, persistedUser, exchangeRateByDate, tenantCurrencies, pipeline, productFieldResponse)
        .map(tuples -> {

          ExchangeRateResponse exchangeRateResponse = tuples.getT4();

          var actualValue = getActualValue(pipelineStage.getType(), deal.getActualValue(), activationRequest.getActualValue(), exchangeRateResponse,
              activationRequest.getActualClosureDate());
          var estimatedValue = getEstimatedValue(pipelineStage.getType(), deal.getEstimatedValue(), activationRequest.getEstimatedValue());

          var dealProducts = getDealProductsToUpdate(tuples.getT1(), loggedInUser, pipelineStage.getType(), deal.getDealProducts(),
              activationRequest.getProducts(), tuples.getT7(), sourceMetaInfo);

          var updatableDeal = deal
              .withPipelineStages(pipelineStages, sourceMetaInfo, deal.getUpdatedAt())
              .withDealProductsAndEstimatedValueAndActualValue(dealProducts, loggedInUser, tuples.getT2(), estimatedValue, actualValue,
                  sourceMetaInfo, exchangeRateResponse, activationRequest.getActualClosureDate())
              .withMetaInfo(metaInfo.update(deal.getUpdatedAt(), metaInfo.getMeetingScheduledOn(), metaInfo.getTaskDueOn(), sourceMetaInfo));

          var dealWithUpdatedPipelineStages = dealRepository.saveAndFlush(updatableDeal);

          DealRequestV2 newDealRequestV2 = toDealRequestV2(dealWithUpdatedPipelineStages);
          raiseDealUpdatedEvent(dealWithUpdatedPipelineStages);
          raiseDealUpdatedEventV2(newDealRequestV2, oldDealRequestV2, null, UPDATED, loggedInUser, true, true,
              tuples.getT5());

          Map<Long, PipelineStageResponse> stageByStageId = tuples.getT6().getStages().stream()
              .collect(toMap(pipelineStageResponse -> pipelineStageResponse.getId(), pipelineStageResponse -> pipelineStageResponse));
          return dealWithUpdatedPipelineStages.getPipelineStages()
              .stream()
              .map(pipelineStage1 -> {
                PipelineStageResponse pipelineStageResponse = stageByStageId.get(pipelineStage1.getPipelineStageId());
                return pipelineStage1.with(pipelineStageResponse.getName(), pipelineStageResponse.getPosition(),
                    pipelineStageResponse.getDescription(), pipelineStageResponse.getWinLikelihood());
              })
              .collect(toList());
        });
  }

  private Mono<User> createUserIfNotExist(User loggedInUser, String authenticationToken) {
    return userFacade.tryGetUserByIdAndTenantId(loggedInUser.getId(), loggedInUser.getTenantId())
        .map(user -> Mono.just(user))
        .orElseGet(() -> {
              return userService.getUserDetails(loggedInUser.getId(), authenticationToken)
                  .map(user -> userFacade.getExistingOrCreateNewUser(user, loggedInUser.getTenantId()));
            }
        );
  }

  private List<DealProduct> getDealProductsToUpdate(List<Product> productResponse, User loggedInUser, StageTypes type,
      List<DealProduct> persistedDealProducts,
      List<com.sling.sales.deal.common.dto.Product> requestedDealProducts,
      List<ProductFieldResponse> productFieldResponse,
      Source source) {
    if (!CLOSED_WON.equals(type)) {
      return persistedDealProducts;
    }

    return getDealProductsToUpdate(productResponse, loggedInUser, requestedDealProducts, persistedDealProducts, loggedInUser, productFieldResponse,
        source);
  }

  private ForexMoney getActualValue(StageTypes type, ForexMoney persistedActualValue, com.sling.sales.deal.common.dto.Money requestedActualValue,
      ExchangeRateResponse exchangeRateResponse, Date actualValueExchangeDate) {
    if (!CLOSED_WON.equals(type)) {
      return persistedActualValue;
    }
    if (ObjectUtils.isNotEmpty(requestedActualValue)) {
      boolean forexEnabled = exchangeRateResponse != null && exchangeRateResponse.isForexEnabled();
      Map<Long, Double> exchangeRate = forexEnabled ? exchangeRateResponse.getExchangeRate() : emptyMap();
      return new ForexMoney(requestedActualValue.getCurrencyId(), requestedActualValue.getValue(), exchangeRate, actualValueExchangeDate);
    }
    return persistedActualValue;
  }

  private ForexMoney getEstimatedValue(StageTypes type, ForexMoney persistedEstimatedValue,
      com.sling.sales.deal.common.dto.Money requestedEstimatedValue) {
    if (!CLOSED_WON.equals(type)) {
      return persistedEstimatedValue;
    }
    if (ObjectUtils.isNotEmpty(requestedEstimatedValue)) {
      return new ForexMoney(requestedEstimatedValue.getCurrencyId(), requestedEstimatedValue.getValue(), persistedEstimatedValue.getExchangeRate(),
          persistedEstimatedValue.getExchangeDate());
    }
    return persistedEstimatedValue;
  }


  public Mono<ShareRule> createOrUpdateShareRule(ShareRuleRequest shareRuleToCreate) {
    List<ShareRule> shareRules;
    if (shareRuleToCreate.isCheckPermission()) {
      shareRules = dealSharingFacade.getShareRulesByFromToAndEntityId(shareRuleToCreate.getSharedBy(),
          shareRuleToCreate.getDealOwnerId(), shareRuleToCreate.getSharedTo(), shareRuleToCreate.getSharedWithId(),
          shareRuleToCreate.getDealIdToShare());
    } else {
      log.info("get ShareRules By From To And EntityId For ReportingManagers");
      shareRules = dealSharingFacade.getShareRulesByFromToAndEntityIdForReportingManagers(shareRuleToCreate.getSharedBy(),
          shareRuleToCreate.getDealOwnerId(), shareRuleToCreate.getSharedTo(), shareRuleToCreate.getSharedWithId());
    }
    String authenticationToken =
        shareRuleToCreate.getCurrentUserToken() != null ? shareRuleToCreate.getCurrentUserToken() : authService.getAuthenticationToken();
    shareRuleToCreate.setCurrentUserToken(authenticationToken);
    var currentUser = shareRuleToCreate.getCurrentUser() != null ? shareRuleToCreate.getCurrentUser() : authService.getLoggedInUser();
    shareRuleToCreate.setCurrentUser(currentUser);
    if (!shareRules.isEmpty()) {
      log.info("share rule not empty");
      var shareRule = shareRules.get(0);
      Action newAction = Action.toUserAction(shareRuleToCreate.getActions());
      if (!shareRule.getGrantedActions().equals(newAction)) {
        Action existingAction = shareRule.getGrantedActions();
        Action action = mergeActions(newAction, existingAction);
        shareRuleToCreate.setActions(com.sling.sales.deal.common.dto.Action.toAction(action));
        return updateSystemShareRule(shareRule.getId(), shareRuleToCreate);
      }
    } else {
      log.info("share rule is empty creating new");
      shareRuleToCreate.setViaType("WORKFLOW");
      return createShareRule(shareRuleToCreate);
    }
    return Mono.just(new ShareRule());
  }

  public Mono<ShareRule> createOrUpdateShareRuleForNotes(ShareRuleRequest shareRuleToCreate) {
    List<ShareRule> shareRules = dealSharingFacade.getShareRulesByFromToAndEntityId(shareRuleToCreate.getSharedBy(),
        shareRuleToCreate.getDealOwnerId(), shareRuleToCreate.getSharedTo(), shareRuleToCreate.getSharedWithId(),
        shareRuleToCreate.getDealIdToShare());
    String authenticationToken = authService.getAuthenticationToken();
    shareRuleToCreate.setCurrentUserToken(authenticationToken);
    shareRuleToCreate.setCurrentUser(authService.getLoggedInUser());
    Mono<Tuple2<com.sling.sales.deal.common.dto.Action, Boolean>> existingActions = userService.getExistingActions(
        shareRuleToCreate.getSharedWithId(), authenticationToken);
    return existingActions.map(tuple2 -> {
      if (tuple2.getT2()) {
        shareRuleToCreate.setActions(tuple2.getT1());
      }
      if (!shareRules.isEmpty()) {
        var shareRule = shareRules.get(0);
        Action newAction = Action.toUserAction(shareRuleToCreate.getActions());
        if (!shareRule.getGrantedActions().equals(newAction)) {
          Action action = mergeActions(newAction, shareRule.getGrantedActions());
          shareRuleToCreate.setActions(com.sling.sales.deal.common.dto.Action.toAction(action));
          return updateSystemShareRule(shareRule.getId(), shareRuleToCreate);
        }
      } else {
        return createShareRule(shareRuleToCreate);
      }
      return Mono.just(new ShareRule());
    }).flatMap(shareRuleMono -> shareRuleMono);
  }

  private Action mergeActions(Action newAction, Action existingAction) {
    Arrays.stream(Action.class.getDeclaredFields())
        .filter(field -> field.getType().equals(boolean.class))
        .forEach(field -> {
          try {
            field.setAccessible(true);
            field.set(newAction, ((boolean) field.get(existingAction) || (boolean) field.get(newAction)));
          } catch (IllegalAccessException e) {
            log.error("Error while updating share rule , Error: {}", e.getMessage());
            throw new RuntimeException(e);
          }
        });
    return newAction;
  }

  public Mono<ShareRule> createShareRule(ShareRuleRequest shareRuleToCreate) {
    if (isSpecificDealBeingShared(shareRuleToCreate)) {
      Deal dealToShare = getDealToShare(shareRuleToCreate.getDealIdToShare(), shareRuleToCreate.getCurrentUser());

      var shareRuleForSingleDealRequest = new ShareRuleForSingleDealRequest();
      shareRuleForSingleDealRequest.setName(shareRuleToCreate.getName());
      shareRuleForSingleDealRequest.setDescription(shareRuleToCreate.getDescription());
      shareRuleForSingleDealRequest.setSharedTo(shareRuleToCreate.getSharedTo());
      shareRuleForSingleDealRequest.setSharedWithId(shareRuleToCreate.getSharedWithId());
      shareRuleForSingleDealRequest.setActions(shareRuleToCreate.getActions());
      shareRuleForSingleDealRequest.setFromId(shareRuleToCreate.getDealOwnerId());
      shareRuleForSingleDealRequest.setCurrentUser(shareRuleToCreate.getCurrentUser());
      shareRuleForSingleDealRequest.setCurrentUserToken(shareRuleToCreate.getCurrentUserToken());
      shareRuleForSingleDealRequest.setViaType(shareRuleToCreate.getViaType());
      return dealSharingFacade.createShareRuleForDeal(dealToShare, shareRuleForSingleDealRequest);
    }
    return dealSharingFacade.createShareRule(shareRuleToCreate);
  }

  public Mono<ShareRule> createShareRuleForDeal(
      long dealId, ShareRuleForSingleDealRequest shareRuleToCreate) {
    Deal dealToShare = getDealToShare(dealId, null);

    return dealSharingFacade.createShareRuleForDeal(dealToShare, shareRuleToCreate);
  }

  public Mono<ShareRule> createSystemDefaultShareRule(
      long dealId, ShareRuleForSingleDealRequest shareRuleToCreate, Long meetingId) {
    var dealToShare = dealRepository.findOne(withDealId(dealId))
        .orElseThrow(DealNotFoundException::new);

    return dealSharingFacade
        .createShareRuleForDeal(dealToShare, shareRuleToCreate)
        .flatMap(shareRule -> {
          entityShareRuleService.createMeetingShareRuleIfAbsent(MEETING, meetingId, shareRule);
          return Mono.just(shareRule);
        });
  }

  public Mono<ShareRule> updateSystemShareRule(long shareRuleId, ShareRuleRequest shareRuleToUpdate) {
    if (isSpecificDealBeingShared(shareRuleToUpdate)) {
      Deal dealToShare = getDealToShare(shareRuleToUpdate.getDealIdToShare(), shareRuleToUpdate.getCurrentUser());

      return dealSharingFacade.updateSystemShareRule(shareRuleId, shareRuleToUpdate, dealToShare);
    }
    return dealSharingFacade.updateSystemShareRule(shareRuleId, shareRuleToUpdate, null);
  }

  public Mono<ShareRule> updateShareRule(long shareRuleId, ShareRuleRequest shareRuleToUpdate) {
    if (isSpecificDealBeingShared(shareRuleToUpdate)) {
      Deal dealToShare = getDealToShare(shareRuleToUpdate.getDealIdToShare(), shareRuleToUpdate.getCurrentUser());

      return dealSharingFacade.updateShareRule(shareRuleId, shareRuleToUpdate, dealToShare);
    }
    return dealSharingFacade.updateShareRule(shareRuleId, shareRuleToUpdate, null);
  }

  @Transactional
  public void removeMeetingAssociatedShareRule(Long meetingId, Long dealId, ShareRuleForSingleDealRequest shareRuleRequest) {
    entityShareRuleService
        .getByMeetingRelatedShareRule(meetingId, dealId, shareRuleRequest.getSharedWithId())
        .ifPresent(entityShareRule -> {
          dealSharingFacade.delete(entityShareRule.getShareRule());
        });
  }


  public ShareRuleParticipants getParticipantsForShareRule(long shareRuleId) {
    return dealSharingFacade.getParticipantsForShareRule(shareRuleId);
  }

  @Transactional
  public void purgeDeal(Long dealId, Long tenantId, Long userId) {
    dealRepository
        .findOne(belongToTenant(tenantId).and(withDealId(dealId)))
        .map(
            dealToDelete -> {
              dealRepository.delete(dealToDelete);
              return dealToDelete;
            })
        .map(
            deletedDeal -> {
              raiseDealDeletedEvent(deletedDeal, userId, true);
              return deletedDeal;
            });
  }

  public List<EstimatedValue> getEstimatedValueWithCurrencyForContact(
      long contactId, long baseConversionCurrencyId, StageTypes forecastingType) {
    var associatedDeals = getDealsAssociatedWithContact(contactId);
    return getEstimatedValuesForDeals(baseConversionCurrencyId, forecastingType, associatedDeals);
  }

  public List<EstimatedValue> getEstimatedValueWithCurrencyForCompany(
      long companyId, long baseConversionCurrencyId, StageTypes forecastingType) {
    var associatedDeals = getDealsAssociatedWithCompany(companyId);
    return getEstimatedValuesForDeals(baseConversionCurrencyId, forecastingType, associatedDeals);
  }

  @Transactional
  public void resetAssociatedContacts(Long dealId, Long tenantId, Long userId, List<IdName> associatedContacts) {
    dealRepository
        .findOne(belongToTenant(tenantId).and(withDealId(dealId))).map(
            dealToUpdate -> {
              log.debug("Associate contact reset for dealId {} tenantId {} contacts", dealId, tenantId, associatedContacts);
              dealToUpdate.setAssociatedContacts(associatedContacts);
              dealRepository.saveAndFlush(dealToUpdate);
              return dealToUpdate;
            })
        .map(
            updatedDeal -> {
              raiseDealUpdatedEvent(updatedDeal);
              raiseDealAssociatedContactEvent(updatedDeal, userId);
              return updatedDeal;
            });
  }

  @Transactional
  public void removeCompanyAssociation(Long dealId, Long tenantId) {
    dealRepository
        .findOne(belongToTenant(tenantId).and(withDealId(dealId)))
        .ifPresent(dealToUpdate -> {
          log.info("Removing company association for deal-{}, tenant-{}", dealId, tenantId);
          dealToUpdate.setCompany(null);
          raiseDealUpdatedEvent(dealRepository.saveAndFlush(dealToUpdate));
        });
  }

  @Transactional
  public Mono<Deal> reassignTo(long dealId, long assignedTo, Metadata metadata) {
    User loggedInUser = authService.getLoggedInUser();
    var authenticationToken = authService.getAuthenticationToken();
    var sourceMetaInfo = authService.getSource();
    var loggedInUserDetailsMono =
        userService.getUserDetails(loggedInUser.getId(), authenticationToken);
    Mono<User> assignedToMono = userService.getUserDetails(assignedTo, authenticationToken);
    Mono<List<TenantCurrencyResponse>> tenantCurrenciesMono = currencyService.getTenantCurrencies(authenticationToken);

    return reassignDeal(dealId, metadata, loggedInUser, loggedInUserDetailsMono, assignedToMono, true, true,
        sourceMetaInfo, tenantCurrenciesMono).map(tuple -> tuple.getT1());
  }

  private Mono<Tuple2<Deal, Long>> reassignDeal(long dealId, Metadata metadata, User loggedInUser, Mono<User> loggedInUserDetailsMono,
      Mono<User> assignedToMono,
      boolean executeWorkflow, boolean sendEmail, Source sourceMetaInfo, Mono<List<TenantCurrencyResponse>> tenantCurrenciesMono) {

    var permissionBasedQuerySpecification = getDealSpecificationForReassign(loggedInUser);
    var existingDeal = tryGetExistingDeal(dealId, permissionBasedQuerySpecification);

    DealRequestV2 oldRequestV2 = toDealRequestV2(existingDeal);

    return Mono.zip(loggedInUserDetailsMono, assignedToMono, tenantCurrenciesMono)
        .map(tuple -> {

          var owner =
              userFacade.getExistingOrCreateNewUser(
                  tuple.getT1(), loggedInUser.getTenantId());

          var oldOwnerId = existingDeal.getOwnedBy().getId();
          var assignedToUser =
              userFacade.getExistingOrCreateNewUser(
                  tuple.getT2(), owner.getTenantId());
          existingDeal.changeOwner(assignedToUser, loggedInUser, oldOwnerId);
          var metaInfo = existingDeal.getMetaInfo();
          var dealWithMetaInfo = existingDeal
              .withMetaInfo(metaInfo.update(existingDeal.getUpdatedAt(), metaInfo.getMeetingScheduledOn(), metaInfo.getTaskDueOn(), sourceMetaInfo));
          return Tuples.of(dealWithMetaInfo, oldOwnerId, tuple.getT3());
        })
        .map(tuples -> Tuples.of(dealRepository.saveAndFlush(tuples.getT1()), tuples.getT2(), tuples.getT3()))
        .map(
            tuples -> {
              var deal = tuples.getT1();
              raiseEventsOnDealReassign(tuples.getT2(), deal, oldRequestV2, metadata, loggedInUser, executeWorkflow, sendEmail, tuples.getT3());
              Optional<Action> sharedAction = dealSharingFacade.getGrantedActionForUser(loggedInUser, deal.getId(), deal.getOwnedBy());
              deal.setAllowedActionsForUser(loggedInUser, sharedAction);
              return Tuples.of(deal, tuples.getT2());
            });
  }

  private void raiseEventsOnDealReassign(long oldOwnerId, Deal deal, DealRequestV2 oldRequestV2,
      Metadata metadata, User loggedInUser, boolean executeWorkflow, boolean sendEmail, List<TenantCurrencyResponse> tenantCurrencies) {
    raiseDealUpdatedEvent(deal);
    raiseDealAssociatedContactEvent(deal, loggedInUser.getId());
    raiseDealReassignedEvent(deal, oldOwnerId, sendEmail);
    raiseDealUpdatedEventV2(toDealRequestV2(deal), oldRequestV2, metadata, UPDATED, loggedInUser, executeWorkflow, sendEmail, tenantCurrencies);
  }

  @Transactional
  public Mono<ReassignDetail> reassignTo(long dealId, User loggedInUser, Mono<User> loggedInUserMono, Mono<User> assignedToUserMono,
      boolean executeWorkflow, boolean sendEmail, Source sourceMetaInfo, Mono<List<TenantCurrencyResponse>> tenantCurrenciesMono) {

    try {

      return reassignDeal(dealId, null, loggedInUser, loggedInUserMono, assignedToUserMono, executeWorkflow,
          sendEmail, sourceMetaInfo, tenantCurrenciesMono)
          .map(tuple -> {
            return new ReassignDetail(dealId, tuple.getT2(), tuple.getT1().getOwnedBy().getId(), ResultType.SUCCESS);
          });

    } catch (Exception e) {
      log.error(e.getMessage(), e);
      Deal existingDeal = dealRepository.getOne(dealId);
      return Mono.just(new ReassignDetail(dealId, existingDeal.getOwnedBy().getId(), existingDeal.getOwnedBy().getId(), ResultType.ERROR));
    }
  }

  public Deal createSampleDeal(DealRequest dealToCreate, User creator) {
    var persistedUser =
        userFacade.getExistingOrCreateNewUser(
            creator, creator.getTenantId());

    Deal aNewDeal = Deal.createNew(
        dealToCreate.getName(),
        persistedUser,
        new ForexMoney(dealToCreate.getEstimatedValue().getCurrencyId(), dealToCreate.getEstimatedValue().getValue(), emptyMap(), null),
        dealToCreate.getEstimatedClosureOn(),
        null,
        persistedUser,
        null,
        null,
        null,
        null, null, Collections.emptyList(), Collections.emptyList(), null, null, null);
    Deal deal = dealRepository.saveAndFlush(aNewDeal);
    raiseDealCreatedEvent(deal);
    raiseDealCreatedEventV2(deal, persistedUser.getTenantId(), persistedUser.getId(), Collections.emptyList());
    raiseDealAssociatedContactEvent(deal, persistedUser.getId());
    return deal;
  }

  @Transactional
  public void updateMetaInfo(MetaInfo metaInfo, Metadata metadata) {
    dealRepository.incrementDealVersionBy(metaInfo.getDealId(), 1);
    dealRepository
        .findOne(belongToTenant(metaInfo.getTenantId()).and(withDealId(metaInfo.getDealId())))
        .ifPresent(deal -> {
          DealRequestV2 existingDealRequestV2 = toDealRequestV2(deal);
          var existingMetaInfo = deal.getMetaInfo();
          if (existingMetaInfo != null) {
            var updatedMetaInfo = existingMetaInfo.update(
                metaInfo.getLatestActivityCreatedAt(),
                metaInfo.getMeetingScheduledOn(existingMetaInfo.getMeetingScheduledOn()),
                metaInfo.getTaskDueOn(existingMetaInfo.getTaskDueOn()), null
            );
            Deal updatedDeal = dealRepository.saveAndFlush(deal.withMetaInfo(updatedMetaInfo));
            raiseDealMetaInfoUpdatedEvent(updatedDeal);
            Metadata finalMetadata = null;
            if (metadata == null) {
              finalMetadata = new Metadata(
                  authService.getTenantId(),
                  authService.getUserId(),
                  DEAL,
                  metaInfo.getDealId(),
                  UPDATED,
                  null,
                  emptySet(), null
              );
            } else {
              finalMetadata = metadata.withEntityTypeIdAndAction(DEAL, metaInfo.getDealId(), UPDATED);
            }
            DealEventPayloadV2 dealEventPayloadV3 = new DealEventPayloadV2(toDealRequestV2(updatedDeal), existingDealRequestV2, finalMetadata);
            /* Not Publishing V3 Event till separate workflow trigger handle for metainfo fields */
//            dealEventPublisherV3.publishDealMetaInfoUpdatedV3(dealEventPayloadV3);
          }
        });
  }

  private boolean isValidMoneyValue(com.sling.sales.deal.common.dto.Money money) {
    return money != null && money.getCurrencyId() != null && money.getValue() != null;
  }

  private List<EstimatedValue> getEstimatedValuesForDeals(long baseConversionCurrencyId, StageTypes forecastingType, List<Deal> associatedDeals) {
    Stream<Deal> dealWithForecastingType = forecastingType != null ?
        associatedDeals.stream()
            .filter(deal -> deal.getCurrentStage().isPresent() && deal.getCurrentStage().get().getType().equals(forecastingType))
        : associatedDeals.stream();
    var amountStats =
        dealWithForecastingType
            .filter(deal -> deal.getEstimatedValue().getCurrencyId() == baseConversionCurrencyId)
            .map(Deal::getEstimatedValue)
            .mapToDouble(ForexMoney::getAmount)
            .summaryStatistics();

    return singletonList(new EstimatedValue(
        amountStats.getCount(),
        new com.sling.sales.deal.common.dto.Money(baseConversionCurrencyId, amountStats.getSum())));
  }

  private boolean isSpecificDealBeingShared(ShareRuleRequest shareRuleToCreate) {
    return shareRuleToCreate.getDealIdToShare() != null;
  }

  private List<Deal> getDealsAssociatedWithContact(long contactId) {
    User loggedInUser = authService.getLoggedInUser();
    return contactRepository
        .findContactByIdAndTenantId(contactId, loggedInUser.getTenantId())
        .map(
            contact ->
                dealRepository.findAll(
                    getSpecificationsBasedOnReadPrivileges(loggedInUser)
                        .and(isAssociatedWithContact(contact))))
        .orElse(emptyList());
  }

  private List<Deal> getDealsAssociatedWithCompany(long companyId) {
    User loggedInUser = authService.getLoggedInUser();
    return companyRepository.findByIdAndTenantId(companyId, loggedInUser.getTenantId())
        .map(company ->
            dealRepository.findAll(
                getSpecificationsBasedOnReadPrivileges(loggedInUser)
                    .and(isAssociatedWithCompany(companyId))))
        .orElse(emptyList());
  }

  private Deal getDealToShare(long dealId, User loggedInUser) {
    var user = loggedInUser != null ? loggedInUser : authService.getLoggedInUser();

    var permissionBasedQuerySpecification = getSpecificationsBasedOnSharingPrivileges(user);
    return tryGetExistingDeal(dealId, permissionBasedQuerySpecification);
  }
  private Tuple7<User, Set<Contact>, List<Product>, List<PipelineStage>, Optional<Company>, List<TenantCurrencyResponse>, Currency>
  getDetailsOfDealPropertiesFromRequest(
      DealRequest dealRequest, User loggedInUser, String authenticationToken, Deal existingDeal, Metadata metadata) {
    var ownedById = loggedInUser.getId();
    if (nonNull(dealRequest.getOwnedBy())) {
      ownedById = dealRequest.getOwnedBy().getId();
    }
    if (isNull(dealRequest.getOwnedBy()) && nonNull(existingDeal)) {
      ownedById = existingDeal.getOwnedBy().getId();
    }
    var ownerDetailsMono =
        userService.getUserDetails(ownedById, authenticationToken).block();
    Tenant tenant = userService.getTenant(authenticationToken).block();
    var tenantCurrencyMono = currencyService.getCurrencyByName(tenant.getCurrency(), authenticationToken).block();

    var tenantCurrencies = currencyService.getTenantCurrencies(authenticationToken).block();

    Set<Contact> readableContactsMono;
    if (dealRequest.getRequestType().equals(JSON_PATCH)) {
      Set<Long> contacts = dealRequest.getAssociatedContacts().stream().map(com.sling.sales.deal.common.dto.Contact::getId)
          .collect(Collectors.toSet());
      readableContactsMono = contacts.isEmpty() ? emptySet() : contactService.getContactsByIds(contacts, authenticationToken).block();
    } else {
      if (dealRequest.getOperation() == FieldOperation.REPLACE) {
        readableContactsMono = getContactsDetails(dealRequest.getAssociatedContacts(), emptySet(),
            authenticationToken).block();
      } else {
        readableContactsMono = getContactsDetails(dealRequest.getAssociatedContacts(),
            existingDeal != null ? existingDeal.getAssociatedContacts() : emptySet(),
            authenticationToken).block();
      }
    }
    var productMono =
        getProductDetails(
            authenticationToken, dealRequest.getProducts()).block();

    var companyMono =
        getCompanyDetails(dealRequest.getCompany(),
            existingDeal != null ? existingDeal.getCompany() : null,
            authenticationToken, metadata).block();

    var pipelineStagesMono =
        getDetailsForPipelineStages(dealRequest.getPipeline(),
            existingDeal != null ? existingDeal.getPipelineStages() : emptyList(),
            loggedInUser,
            authenticationToken, metadata, dealRequest.getPipelineStageReason()).block();

    return Tuples.of(
        ownerDetailsMono,
        readableContactsMono,
        productMono,
        pipelineStagesMono,
        companyMono,
        tenantCurrencies, tenantCurrencyMono);
  }

  private User setCreatedByUpdatedBy(com.sling.sales.deal.common.dto.User user, User loggedInUser, String authenticationToken,
      ErrorResource errorResource) {

    User existingUser = userFacade.getExistingUser(user.getId(), loggedInUser.getTenantId());
    if (isNull(existingUser)) {
      return userService.getUserDetails(user.getId(), authenticationToken, errorResource).block();
    }
    return existingUser;
  }

  private Mono<
      Tuple7<User, Set<Contact>, List<Product>, List<PipelineStage>, Optional<Company>, List<TenantCurrencyResponse>, Currency>>
  getDetailsOfDealPropertiesFromImportRequest(
      DealRequest dealRequest, User loggedInUser, String authenticationToken, Metadata metadata) {

    var ownedById = dealRequest.getOwnedBy().getId();

    var ownerDetailsMono =
        userService.getUserDetails(ownedById, authenticationToken);
    var tenantCurrencyMono = userService.getTenant(authenticationToken)
        .flatMap(tenant -> currencyService.getCurrencyByName(tenant.getCurrency(), authenticationToken));
    var productMono =
        getProductDetails(
            authenticationToken, dealRequest.getProducts());

    var companyMono =
        getCompanyDetails(dealRequest.getCompany(), null,
            authenticationToken, metadata);

    var pipelineStagesMono =
        getDetailsForPipelineStages(dealRequest.getPipeline(), emptyList(),
            loggedInUser,
            authenticationToken, metadata, dealRequest.getPipelineStageReason());

    var tenantCurrencies = currencyService.getTenantCurrencies(authenticationToken);

    return Mono.zip(
        ownerDetailsMono,
        Mono.just(dealRequest.getAssociatedContacts().stream()
            .map(contact -> new Contact(contact.getId(), contact.getName()).withTenantId(loggedInUser.getTenantId())).collect(Collectors.toSet())),
        productMono,
        pipelineStagesMono,
        companyMono,
        tenantCurrencies,
        tenantCurrencyMono);
  }

  private Set<Long> getProductIds(List<com.sling.sales.deal.common.dto.Product> products) {
    Set<Long> productIds = new LinkedHashSet<>();
    if (ObjectUtils.isNotEmpty(products)) {
      products
          .stream()
          .forEach(product1 -> productIds.add(product1.getId()));
    }
    return productIds;
  }

  private Mono<Set<Contact>> getContactsDetails(List<com.sling.sales.deal.common.dto.Contact> requestedContacts, Set<Contact> existingContacts,
      String authenticationToken) {

    if (requestedContacts.isEmpty()) {
      return existingContacts.isEmpty() ? Mono.just(emptySet()) : Mono.just(existingContacts);
    }

    List<Long> existingContactIds = existingContacts.stream().map(contact -> contact.getId()).collect(toList());
    var requestedContactIds = requestedContacts.stream().map(contact -> contact.getId()).collect(toList());

    var addedContacts = requestedContacts.stream()
        .filter(contact -> !existingContactIds.contains(contact.getId()))
        .map(contact -> contact.getId())
        .collect(toSet());
    var removedContacts = existingContactIds.stream()
        .filter(existingContactId -> !requestedContactIds.contains(existingContactId))
        .collect(toSet());
    Set<Long> changedContacts = Sets.union(addedContacts, removedContacts);

    if (changedContacts.isEmpty()) {
      return Mono.just(new LinkedHashSet<>());
    }
    return contactService.getContactsByIds(changedContacts, authenticationToken);
  }

  private Mono<List<Product>> getProductDetails(
      String authenticationToken,
      List<com.sling.sales.deal.common.dto.Product> products) {
    Set<Long> productIds = getProductIds(products);
    if (ObjectUtils.isEmpty(productIds)) {
      return Mono.just(Collections.emptyList());
    }
    return productService
        .getProductById(productIds, authenticationToken);
  }

  private Mono<Optional<Company>> getCompanyDetails(
      com.sling.sales.deal.common.dto.Company requestedCompany, Company existingCompany, String authenticationToken, Metadata metadata) {

    if (isNull(requestedCompany) && metadata == null) {
      return Mono.just(Optional.empty());
    }
    if (isNull(requestedCompany) && metadata != null) {
      return isNull(existingCompany) ? Mono.just(Optional.empty()) : Mono.just(existingCompany).map(Optional::of);
    }
    if (nonNull(existingCompany) && nonNull(requestedCompany) && requestedCompany.getId().equals(existingCompany.getId())) {
      return Mono.just(Optional.of(existingCompany));
    }
    return companyService
        .getCompanyById(requestedCompany.getId(), authenticationToken)
        .map(Optional::of);
  }

  private Mono<List<PipelineStage>> getDetailsForPipelineStages(
      com.sling.sales.deal.common.dto.Pipeline pipeline,
      List<PipelineStage> existingPipelineStages,
      User loggedInUser,
      String authenticationToken, Metadata metadata, String pipelineStageReason) {

    if (isNull(pipeline) && metadata == null) {
      return Mono.just(emptyList());
    }
    if (isNull(pipeline) && metadata != null) {
      return Mono.just(existingPipelineStages);
    }
    return dealPipelineFacade.managePipelineStages(
        existingPipelineStages,
        pipeline.getId(),
        pipeline.pipelineStageId(),
        loggedInUser.getTenantId(),
        authenticationToken, pipelineStageReason);
  }

  private Deal tryGetExistingDealWithAllowedAction(User loggedInUser, long dealId, Specification<Deal> permissionBasedQuerySpecification) {
    Deal deal = tryGetExistingDeal(dealId, permissionBasedQuerySpecification);
    Optional<Action> sharedAction = dealSharingFacade.getGrantedActionForUser(loggedInUser, deal.getId(), deal.getOwnedBy());
    return deal.setAllowedActionsForUser(loggedInUser, sharedAction);
  }

  private Deal tryGetExistingDeal(
      long dealId, Specification<Deal> permissionBasedQuerySpecification) {
    return dealRepository
        .findOne(permissionBasedQuerySpecification.and(withDealId(dealId)))
        .orElseThrow(DealNotFoundException::new);
  }


  private Company getExistingOrCreateNewCompany(Optional<Company> optionalCompany, long tenantId) {
    return optionalCompany
        .map(
            companyDetails ->
                companyRepository
                    .findByIdAndTenantId(companyDetails.getId(), tenantId)
                    .map(
                        company -> {
                          var updatedCompany = company.withName(companyDetails.getName());
                          return companyRepository.saveAndFlush(updatedCompany);
                        })
                    .orElse(companyRepository.saveAndFlush(companyDetails.withTenantId(tenantId))))
        .orElse(null);
  }

  private Contact getExistingOrCreateNewContact(Contact contactDetails) {
    return contactRepository
        .findContactByIdAndTenantId(contactDetails.getId(), contactDetails.getTenantId())
        .map(
            contact -> {
              var updatedContact = contact.withName(contactDetails.getName());
              return contactRepository.save(updatedContact);
            })
        .orElse(contactRepository.saveAndFlush(contactDetails));
  }

  private Specification<Deal> getSpecificationsBasedOnReadPrivileges(User reader) {
    if (!reader.canQueryHisDeals() && !reader.canQueryAllDeals()) {
      throw new InsufficientPrivilegeException();
    }

    if (reader.canQueryAllDeals()) {
      return belongToTenant(reader.getTenantId());
    }

    Specification<Deal> shareRuleSpecifications = getSpecificationBasedOnSharedDeals(reader, false);
    var userSpecificSpecifications =
        ownedBy(reader.getId()).or(shareRuleSpecifications);

    return belongToTenant(reader.getTenantId()).and(userSpecificSpecifications);
  }

  private Specification<Deal> getSpecificationsBasedOnSharingPrivileges(User reader) {
    if (reader.canUpdateAllDeals()) {
      return belongToTenant(reader.getTenantId());
    }
    if (reader.canReshare()) {
      return belongToTenant(reader.getTenantId());
    }
    return belongToTenant(reader.getTenantId()).and(ownedBy(reader.getId()));
  }

  private Specification<Deal> getSpecificationsBasedOnUpdatePrivileges(User reader) {
    if (!reader.canUpdateHisDeals() && !reader.canUpdateAllDeals()) {
      throw new InsufficientPrivilegeException();
    }

    if (reader.canUpdateAllDeals()) {
      return belongToTenant(reader.getTenantId());
    }

    var specificationBasedOnSharedDeals = getSpecificationBasedOnSharedDeals(reader, true);

    var userSpecificSpecifications =
        ownedBy(reader.getId()).or(specificationBasedOnSharedDeals);

    return belongToTenant(reader.getTenantId()).and(userSpecificSpecifications);
  }

  Specification<Deal> getSpecificationBasedOnSharedDeals(
      User reader, boolean requiresUpdateRights) {
    var shareRules = dealSharingFacade.getRulesSharedWithUser(reader, requiresUpdateRights, null, null);
    List<Long> sharedDealIds = getSharedDealIds(shareRules);

    var dealOwnerIds =
        getDealOwnerIds(shareRules);

    return haveIdsIn(sharedDealIds).or(haveDealOwnersIn(dealOwnerIds));
  }

  private Predicate<ShareRule> byRulesForSpecificDeals() {
    return shareRule -> shareRule.getDealId() != null;
  }

  private Predicate<ShareRule> byRulesForDealOwners() {
    return shareRule -> shareRule.getDealId() == null;
  }

  private void raiseDealCreatedEvent(Deal deal) {
    var contacts =
        deal.getAssociatedContacts().stream()
            .map(contact -> new com.sling.sales.deal.mq.event.Contact(contact.getId(), contact.getName()))
            .collect(toList());

    List<com.sling.sales.deal.mq.event.ProductWithoutCategory> dealProducts = deal.getDealProducts().stream()
        .map(product1 -> new com.sling.sales.deal.mq.event.ProductWithoutCategory(product1.getProductId(), product1.getProductName(),
            new com.sling.sales.deal.mq.event.Money(product1.getPrice().getCurrencyId(), product1.getPrice().getAmount()),
            product1.getDiscount(), product1.getQuantityInDecimal(), product1.getUnits())).collect(toList());

    var pipeline =
        deal.getCurrentStage()
            .map(
                stage ->
                    new com.sling.sales.deal.mq.event.Pipeline(
                        stage.getPipeline().getId(), stage.getPipeline().getName()))
            .orElse(null);

    var pipelineStage =
        deal.getCurrentStage()
            .map(
                stage ->
                    new com.sling.sales.deal.mq.event.PipelineStage(stage.getPipelineStageId(), stage.getName()))
            .orElse(null);

    var dealCompany = deal.getCompany();
    var company =
        dealCompany != null
            ? new com.sling.sales.deal.mq.event.Company(deal.getCompany().getId(), deal.getCompany().getName())
            : null;

    var forecastingType = deal.getCurrentStage()
        .map(s -> s.getType().name())
        .orElse(null);

    var actualValue =
        isNull(deal.getActualValue()) ? null
            : new com.sling.sales.deal.mq.event.Money(deal.getActualValue().getCurrencyId(),
                deal.getActualValue().getAmount());

    var source = deal.getSource() == null ? null
        : new com.sling.sales.deal.domain.field.dto.PicklistValue(deal.getSource().getId(), deal.getSource().getDisplayName());
    var campaign = deal.getCampaign() == null ? null
        : new com.sling.sales.deal.domain.field.dto.PicklistValue(deal.getCampaign().getId(), deal.getCampaign().getDisplayName());

    DealUtm dealUtm = deal.getDealUtms().stream().findFirst().orElseGet(() -> new DealUtm());

    var subSource = dealUtm.getSubSource();
    var utmSource = dealUtm.getUtmSource();
    var utmCampaign = dealUtm.getUtmCampaign();
    var utmMedium = dealUtm.getUtmMedium();
    var utmContent = dealUtm.getUtmContent();
    var utmTerm = dealUtm.getUtmTerm();

    ForexMoney actualValue1 = deal.getActualValue();
    Long currencyId = null;
    Double value = null;
    if (actualValue1 != null) {
      currencyId = actualValue1.getCurrencyId();
      value = actualValue1.getAmount();
    }

    DealCreatedEvent dealCreatedEvent =
        new DealCreatedEvent(
            deal.getId(),
            deal.getName(),
            deal.getEstimatedValue(),
            (currencyId != null && value != null) ? deal.getActualValue() : null,
            deal.getEstimatedClosureOn(),
            contacts,
            pipeline,
            pipelineStage,
            forecastingType,
            company,
            ObjectUtils.isEmpty(deal.getImportedBy()) ? null : deal.getImportedBy().getId(),
            ObjectUtils.isEmpty(deal.getImportedBy()) ? null : deal.getImportedBy().getName(),
            deal.getOwnedBy().getId(),
            deal.getOwnedBy().getName(),
            deal.getCreatedBy().getId(),
            deal.getCreatedBy().getName(),
            deal.getUpdatedBy().getId(),
            deal.getUpdatedBy().getName(),
            deal.getTenantId(),
            deal.getCreatedAt(),
            deal.getUpdatedAt(),
            deal.getActualClosureDate(),
            deal.getVersion(), deal.getReasonForClosing(),
            deal.getMetaInfo().getIsNew(),
            deal.getMetaInfo().getLatestActivityCreatedAt(),
            deal.getMetaInfo().getTaskDueOn(),
            deal.getMetaInfo().getMeetingScheduledOn(),
            source,
            campaign, deal.getCustomFieldValues(), dealProducts, deal.getMetaInfo().getCreatedViaId(),
            deal.getMetaInfo().getCreatedViaName(),
            deal.getMetaInfo().getCreatedViaType(), subSource, utmSource, utmCampaign, utmMedium, utmContent, utmTerm);
    dealEventPublisher.publishDealCreated(dealCreatedEvent);
  }

  private void raiseDealUpdatedEvent(Deal deal) {
    dealEventPublisher.publishDealUpdated(getDealUpdatedEvent(deal));
  }

  private void raiseDealMetaInfoUpdatedEvent(Deal deal) {
    dealEventPublisher.publishDealMetaInfoUpdated(getDealUpdatedEvent(deal));
  }

  private DealUpdatedEvent getDealUpdatedEvent(Deal deal) {
    var contacts =
        deal.getAssociatedContacts().stream()
            .map(contact -> new com.sling.sales.deal.mq.event.Contact(contact.getId(), contact.getName()))
            .collect(toList());

    List<com.sling.sales.deal.mq.event.ProductWithoutCategory> dealProducts = deal.getDealProducts().stream()
        .map(product1 -> new com.sling.sales.deal.mq.event.ProductWithoutCategory(product1.getProductId(), product1.getProductName(),
            new com.sling.sales.deal.mq.event.Money(product1.getPrice().getCurrencyId(), product1.getPrice().getAmount()),
            product1.getDiscount(), product1.getQuantityInDecimal(), product1.getUnits())).collect(toList());

    var pipeline =
        deal.getCurrentStage()
            .map(
                stage ->
                    new com.sling.sales.deal.mq.event.Pipeline(
                        stage.getPipeline().getId(), stage.getPipeline().getName()))
            .orElse(null);

    var pipelineStage =
        deal.getCurrentStage()
            .map(
                stage ->
                    new com.sling.sales.deal.mq.event.PipelineStage(stage.getPipelineStageId(), stage.getName()))
            .orElse(null);

    var dealCompany = deal.getCompany();
    var company =
        dealCompany != null
            ? new com.sling.sales.deal.mq.event.Company(deal.getCompany().getId(), deal.getCompany().getName())
            : null;

    var forecastingType = deal.getCurrentStage()
        .map(s -> s.getType().name())
        .orElse(null);
    var actualValue =
        isNull(deal.getActualValue()) ? null
            : new com.sling.sales.deal.mq.event.Money(deal.getActualValue().getCurrencyId(),
                deal.getActualValue().getAmount());

    var source = deal.getSource() == null ? null
        : new com.sling.sales.deal.domain.field.dto.PicklistValue(deal.getSource().getId(), deal.getSource().getDisplayName());
    var campaign = deal.getCampaign() == null ? null
        : new com.sling.sales.deal.domain.field.dto.PicklistValue(deal.getCampaign().getId(), deal.getCampaign().getDisplayName());

    DealUtm dealUtm = deal.getDealUtms().stream().findFirst().orElseGet(() -> new DealUtm());

    var subSource = dealUtm.getSubSource();
    var utmSource = dealUtm.getUtmSource();
    var utmCampaign = dealUtm.getUtmCampaign();
    var utmMedium = dealUtm.getUtmMedium();
    var utmContent = dealUtm.getUtmContent();
    var utmTerm = dealUtm.getUtmTerm();

    ForexMoney actualValue1 = deal.getActualValue();
    Long currencyId = null;
    Double value = null;
    if (actualValue1 != null) {
      currencyId = actualValue1.getCurrencyId();
      value = actualValue1.getAmount();
    }

    var event =
        new DealUpdatedEvent(
            deal.getId(),
            deal.getName(),
            deal.getEstimatedValue(),
            (currencyId != null && value != null) ? deal.getActualValue() : null,
            deal.getEstimatedClosureOn(),
            contacts,
            pipeline,
            pipelineStage,
            forecastingType,
            company,
            deal.getOwnedBy().getId(),
            deal.getOwnedBy().getName(),
            deal.getImportedBy() == null ? null : deal.getImportedBy().getId(),
            deal.getImportedBy() == null ? null : deal.getImportedBy().getName(),
            deal.getCreatedBy().getId(),
            deal.getCreatedBy().getName(),
            deal.getUpdatedBy().getId(),
            deal.getUpdatedBy().getName(),
            deal.getTenantId(),
            deal.getCreatedAt(),
            deal.getUpdatedAt(),
            deal.getActualClosureDate(),
            deal.getVersion(), deal.getReasonForClosing(),
            deal.getMetaInfo().getIsNew(),
            deal.getMetaInfo().getLatestActivityCreatedAt(),
            deal.getMetaInfo().getTaskDueOn(),
            deal.getMetaInfo().getMeetingScheduledOn(),
            source,
            campaign, deal.getCustomFieldValues(), dealProducts,
            deal.getMetaInfo().getCreatedViaId(), deal.getMetaInfo().getCreatedViaName(), deal.getMetaInfo().getCreatedViaType(),
            deal.getMetaInfo().getUpdatedViaId(), deal.getMetaInfo().getUpdatedViaName(), deal.getMetaInfo().getUpdatedViaType(),
            subSource, utmSource, utmCampaign, utmMedium, utmContent, utmTerm);

    return event;
  }

  private void raiseDealNameUpdatedEvent(Deal deal) {
    DealNameUpdatedEvent event =
        new DealNameUpdatedEvent(
            deal.getId(), deal.getName(), deal.getTenantId(), deal.getOwnedBy().getId());
    dealEventPublisher.publishDealNameUpdated(event);
  }

  private void raiseDealReassignedEvent(Deal deal, Long oldOwnerId, boolean sendEmail) {
    DealUpdatedEvent dealUpdatedEvent = getDealUpdatedEvent(deal);
    DealReassignedEvent event =
        new DealReassignedEvent(deal.getId(), oldOwnerId, dealUpdatedEvent).withSendEmail(sendEmail);
    dealEventPublisher.publishDealReassigned(event);
  }

  private void raiseDealDeletedEvent(Deal deletedDeal, Long userId, boolean publishUsage) {
    dealEventPublisher.publishDealDeletedEvent(
        new DealDeletedEvent(
            deletedDeal.getId(), deletedDeal.getTenantId(), userId, deletedDeal.getVersion(), publishUsage));
  }

  public boolean findDealsWithAttachedPipeline(long pipelineId) {
    User loggedInUser = authService.getLoggedInUser();
    return dealRepository.isAttached(loggedInUser.getTenantId(), pipelineId);
  }

  public Page<LookUp> lookupDealsForTaskEnable(Optional<String> dealNameOption, Pageable page) {

    var loggedInUser = authService.getLoggedInUser();

    Specification<Deal> specification = getSpecificationWithTaskPrivileges(loggedInUser);

    return dealNameOption
        .map(partialDealName -> {
          Specification<Deal> withDealName = specification.and(containsName(partialDealName));
          return dealLookUpRepository.lookUpByName(withDealName, page);
        })
        .orElse(dealLookUpRepository.lookUpByName(specification, page));
  }

  private Specification<Deal> getSpecificationWithTaskPrivileges(User loggedInUser) {
    if (!loggedInUser.canLookUpDeals()) {
      throw new InsufficientPrivilegeException();
    }
    if (!loggedInUser.hasAccessToTasks()) {
      throw new InsufficientPrivilegeException();
    }

    Specification<Deal> baseSpecification = belongToTenant(loggedInUser.getTenantId());

    if (loggedInUser.canQueryAllDeals()) {
      return baseSpecification;
    }
    return getSpecificationToIncludeTask(loggedInUser, baseSpecification.and(ownedBy(loggedInUser.getId())));
  }

  private Specification<Deal> getSpecificationToIncludeTask(
      User reader, Specification<Deal> baseSpecification) {
    var shareRules = dealSharingFacade.getRulesSharedWithUserWithTaskPrivileges(reader);
    List<Long> sharedDealIds = getSharedDealIds(shareRules);
    if (!sharedDealIds.isEmpty()) {
      baseSpecification = baseSpecification.or(haveIdsIn(sharedDealIds));
    }
    var dealOwnerIds = getDealOwnerIds(shareRules);
    if (!dealOwnerIds.isEmpty()) {
      baseSpecification = baseSpecification.or(haveDealOwnersIn(dealOwnerIds));
    }
    return baseSpecification;
  }

  private List<Long> getDealOwnerIds(List<ShareRule> shareRules) {
    return shareRules.stream()
        .filter(byRulesForDealOwners())
        .flatMap(shareRule -> shareRule.getDealOwners().stream())
        .map(User::getId)
        .collect(toList());
  }

  private List<Long> getSharedDealIds(List<ShareRule> shareRules) {
    return shareRules.stream()
        .filter(byRulesForSpecificDeals())
        .map(shareRule -> shareRule.getDealId())
        .collect(toList());
  }

  private Specification<Deal> getDealSpecificationForReassign(User reader) {
    if (!reader.canUpdateHisDeals() && !reader.canUpdateAllDeals()) {
      throw new InsufficientPrivilegeException();
    }

    if (reader.canUpdateAllDeals() || reader.canReassign()) {
      return belongToTenant(reader.getTenantId());
    }

    return belongToTenant(reader.getTenantId())
        .and(ownedBy(reader.getId()));
  }

  @Transactional
  public void deleteCompany(Long id, Long tenantId) {
    Optional<Company> company = companyRepository.findById(id);
    if (company.isEmpty()) {
      return;
    }
    List<Deal> dealsAssociatedWithCompany = dealRepository.findByCompanyId(id);
    for (Deal deal : dealsAssociatedWithCompany) {
      log.info("Removing company association for deal-{}, tenant-{}", deal.getId(), tenantId);
      deal.setCompany(null);
      raiseDealUpdatedEvent(dealRepository.saveAndFlush(deal));
    }
    companyRepository.deleteById(id);
    log.info("Removing company for event received with companyId {}, tenantId {}", id, tenantId);
  }

  @Transactional
  public void deleteContact(Long id, Long tenantId) {
    Optional<Contact> contact = contactRepository.findContactByIdAndTenantId(id, tenantId);
    if (contact.isEmpty()) {
      log.info("No Contact Found with contactID - {}, tenant - {}", id, tenantId);
      return;
    }
    List<Deal> dealsAssociatedWithContact = dealRepository.findAll(isAssociatedWithContact(contact.get()));
    dealsAssociatedWithContact.forEach(deal -> {
      log.info("Removing contact association for deal-{}, tenant-{}", deal.getId(), tenantId);
      deal.getAssociatedContacts().removeIf(contact1 -> contact1.getId() == id);
      raiseDealUpdatedEvent(dealRepository.saveAndFlush(deal));
    });

    contactRepository.deleteById(id);
    log.info("Removing contact for event received with companyId {}, tenantId {}", id, tenantId);
  }


  private void raiseDealDeletedEventV2(Deal deal, Long tenantId, User user, List<TenantCurrencyResponse> tenantCurrencies, boolean publishUsage) {
    log.info("started publishing delete event V2 for deal id {}", deal.getId());
    DealRequestV2 deletedDealRequestV2 = toDealRequestV2(deal).withDeletedVia(authService.getSource(), user);
    deletedDealRequestV2.populateCurrencyNameAndDisplayName(tenantCurrencies);
    DealEventPayloadV2 dealEventPayloadV2 = new DealEventPayloadV2(null, deletedDealRequestV2,
        new Metadata(tenantId, user.getId(), DEAL, deal.getId(), DELETED,
            null, emptySet(), null).withPublishUsage(publishUsage));
    dealEventPublisherV2.publishDealDeletedEventV2(dealEventPayloadV2);
    log.info("published delete event V2 for deal id {}", deal.getId());
  }

  public DeleteDetail deleteDealById(long dealId, boolean publishUsage) {

    User user = authService.getLoggedInUser();
    long tenantId = user.getTenantId();
    User persistedUser = userFacade.tryGetUserByIdAndTenantId(user.getId(), user.getTenantId())
        .map(user1 -> Mono.just(user1.withPermissions(user.getPermissions(), true)))
        .orElseGet(() -> {
          return userService.getUserDetails(user.getId(), authService.getAuthenticationToken())
              .map(user1 -> userFacade.getExistingOrCreateNewUser(user1, tenantId));
        }).block();

    List<TenantCurrencyResponse> tenantCurrencies = currencyService.getTenantCurrencies(authService.getAuthenticationToken()).block();

    DeleteDetail deleteDetail = deleteDeal(dealId, persistedUser, tenantCurrencies, publishUsage);
    if (deleteDetail.getResult() == ResultType.SUCCESS && publishUsage) {
      log.info("Publishing Tenant Usage event for deal id {} deleted by user id {}, tenantId {}", dealId, user.getId(), user.getTenantId());
      tenantUsageInternalPublisher.publish((new TenantUsageByEntity(this, tenantId, "DEAL")));
    }
    return deleteDetail;
  }

  private DeleteDetail deleteDeal(long dealId, User user, List<TenantCurrencyResponse> tenantCurrencies, boolean publishUsage) {
    long tenantId = user.getTenantId();
    return dealRepository.findOne(withDealId(dealId).and(getSpecificationForDelete(user)))
        .map(deal -> {
          dealSharingFacade.deleteShareRulesByDealId(dealId);
          dealRepository.delete(deal);
          raiseDealDeletedEvent(user, deal, publishUsage);
          raiseDealDeletedEventV2(deal, tenantId, user, tenantCurrencies, publishUsage);
          log.info("DEAL id {} has been deleted by tenantId {} and userId {}", dealId, tenantId, user.getId());
          return new DeleteDetail(dealId, deal.getOwnedBy().getId(), tenantId, ResultType.SUCCESS);
        })
        .orElse(new DeleteDetail(dealId, user.getId(), tenantId, ResultType.ERROR));
  }

  private void raiseDealDeletedEvent(User user, Deal deletedDeal, boolean publishUsage) {
    dealEventPublisher.publishDealDeletedEvent(
        new DealDeletedEvent(deletedDeal.getId(), user.getTenantId(), user.getId(), deletedDeal.getVersion(), publishUsage));
  }

  public long getDealCount(long tenantId) {
    return dealRepository.countByTenantId(tenantId);
  }

  private Specification<Deal> getSpecificationForDelete(User user) {
    if (!user.canDeleteHisDeals() && !user.canDeleteAllDeals()) {
      throw new InsufficientPrivilegeException();
    }

    if (user.canDeleteAllDeals()) {
      return belongToTenant(user.getTenantId());
    }

    return belongToTenant(user.getTenantId()).and(ownedBy(user.getId()));
  }

  public List<UsageRecord> getRecordUsage() {
    return dealRepository.getCountByTenantId();
  }

  public Mono<ShareRuleSummary> deleteShareRule(long shareRuleId) {
    return dealSharingFacade.delete(shareRuleId);
  }

  public void deleteShareRuleForReportingManagers(ReportingManagerShareRuleDeleteEvent reportingManagerShareRuleDeleteEvent) {
    Long fromId = reportingManagerShareRuleDeleteEvent.getOwnerId();
    reportingManagerShareRuleDeleteEvent
        .getUserIds()
        .forEach(userId -> {
          List<ShareRule> shareRulesByFromToAndEntityIdForReportingManagers = dealSharingFacade.getShareRulesByFromToAndEntityIdForReportingManagers(
              USER,
              fromId, USER, userId);
          if (!shareRulesByFromToAndEntityIdForReportingManagers.isEmpty()) {
            ShareRule shareRule = shareRulesByFromToAndEntityIdForReportingManagers.get(0);
            dealSharingFacade.delete(shareRule);
          }
        });
  }

  private Date getFormattedDate(Object dateString, ErrorResource errorResource) {
    if (ObjectUtils.isEmpty(dateString)) {
      return null;
    }
    try {
      return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
          .parse((String) dateString);
    } catch (ParseException e) {
      throw new DomainException(errorResource);
    }
  }

  private com.sling.sales.deal.domain.field.dto.PicklistValue getPickListValue(Map<String, Object> sourceMap) {
    if (ObjectUtils.isEmpty(sourceMap)) {
      return null;
    }
    return new com.sling.sales.deal.domain.field.dto.PicklistValue(Long.valueOf(sourceMap.get("id").toString()), (String) sourceMap.get("name"));
  }

  private Mono<com.sling.sales.deal.domain.user.User> getOwnerByEmail(String ownerEmail, String authenticationToken) {
    return
        userService.getUserDetailsByEmail(ownerEmail, authenticationToken);
  }

  private Mono<Optional<com.sling.sales.deal.common.dto.Money>> getMoney(String currency, String value, String authToken) {
    if (ObjectUtils.isEmpty(value) && ObjectUtils.isEmpty(currency)) {
      return Mono.just(Optional.empty());
    }
    if (ObjectUtils.isEmpty(currency) && ObjectUtils.isNotEmpty(value)) {
      throw new DomainException(ErrorCode.CURRENCY_FIELDS_NOT_PRESENT);
    }
    if (ObjectUtils.isEmpty(value) && ObjectUtils.isNotEmpty(currency)) {
      throw new DomainException(ErrorCode.MONEY_VALUE_NOT_PRESENT);
    }

    return
        currencyService.getCurrencyByName(currency, authToken)
            .map(currency1 ->
                new com.sling.sales.deal.common.dto.Money(currency1.getId(), Double.valueOf(value)))
            .map(Optional::ofNullable);
  }


  private Mono<List<com.sling.sales.deal.common.dto.Product>> getProductsByName(List<com.sling.sales.deal.api.request.Product> productList,
      String authToken) {
    if (ObjectUtils.isEmpty(productList)) {
      return Mono.just(Collections.emptyList());
    }
    List<String> collect1 = productList.stream().map(com.sling.sales.deal.api.request.Product::getName).distinct()
        .collect(Collectors.toList());

    return productService.getProductByNames(collect1, authToken)
        .map(searchResponse1 -> searchResponse1.getContent().stream().map(stringObjectMap -> {
              long productId = Long.valueOf(stringObjectMap.get("id").toString());
              String productName = stringObjectMap.get("name").toString();

              Category category = extractCategory(stringObjectMap.get("category"));
              Map<String, Unit> units = extractUnits(stringObjectMap.get("units"))
                  .stream().collect(Collectors.toMap(Unit::getName, unit -> unit));
              String hsnSacCode = extractString(stringObjectMap.get("hsnSacCode"));
              IdName countryOfOrigin = extractIdName(stringObjectMap.get("countryOfOrigin"));
              Map<String, Object> customFieldValues = extractMap(stringObjectMap.get("customFieldValues"));

              List<com.sling.sales.deal.api.request.Product> productRequestList = productList.stream()
                  .filter(product -> product.getName().equalsIgnoreCase(productName)).collect(toList());
              return productRequestList.stream().map(product -> {
                Double quantity = product.getQuantity();
                if (product.getUnits() != null && !units.containsKey(product.getUnits())) {
                  throw new InactiveProductException(ErrorCode.INVALID_UNIT_ON_PRODUCT);
                }
                return getMoney(product.getProductPriceCurrency(), product.getProductPriceValue().toString(), authToken)
                    .map(money -> new com.sling.sales.deal.common.dto.Product(productId, productName, quantity,
                        new com.sling.sales.deal.common.dto.Money(money.get().getCurrencyId(), money.get().getValue()),
                        product.getDiscount(),
                        category, null, hsnSacCode, countryOfOrigin, product.getUnits() != null ? units.get(product.getUnits()) : null, customFieldValues,
                        true));
              }).collect(toList());
            })
        )
        .map(monoStream -> monoStream.flatMap(List::stream).collect(toList()))
        .flatMap(monos -> Flux.fromIterable(monos).flatMap(productMono -> productMono).collectList())
        .onErrorMap(e -> {
          String message = e.getMessage();
          log.error("Error while fetching User {}", message);
          return new DomainException(ErrorCode.INVALID_PRODUCTS);
        });
  }

  private Category extractCategory(Object categoryPayload) {
    if (ObjectUtils.isNotEmpty(categoryPayload)) {
      Map<String, Object> categoryMap = (Map<String, Object>) categoryPayload;
      return new Category(Long.valueOf(categoryMap.get("id").toString()), categoryMap.get("name").toString());
    }
    return null;
  }

  private List<Unit> extractUnits(Object unitPayload) {
    if (ObjectUtils.isNotEmpty(unitPayload)) {
      List<Map<String, Object>> unitsMap = (List<Map<String, Object>>) unitPayload;
      return unitsMap.stream()
          .map(stringObjectMap -> new Unit(Long.parseLong(stringObjectMap.get("id").toString()), stringObjectMap.get("name").toString()))
          .collect(toList());
    }
    return emptyList();
  }

  private String extractString(Object payload) {
    return ObjectUtils.isNotEmpty(payload) ? payload.toString() : null;
  }

  private IdName extractIdName(Object payload) {
    if (ObjectUtils.isNotEmpty(payload)) {
      Map<String, Object> idNameMap = (Map<String, Object>) payload;
      Long id = Long.valueOf(idNameMap.get("id").toString());
      String name = idNameMap.get("name").toString();
      return new IdName(id, name);
    }
    return null;
  }

  private Map<String, Object> extractMap(Object payload) {
    return ObjectUtils.isNotEmpty(payload) ? (Map<String, Object>) payload : null;
  }


  private Mono<Optional<com.sling.sales.deal.common.dto.Pipeline>> getPipelineByName(Pipeline pipeline, String authToken) {
    if (ObjectUtils.isEmpty(pipeline)) {
      return Mono.just(Optional.empty());
    }
    return pipelineService.getPipelineByName(pipeline.getName(), authToken)
        .map(pipelineResponse -> {
          if (ObjectUtils.isEmpty(pipelineResponse)) {
            throw new DomainException(ErrorCode.PIPELINE_INVALID);
          }
          return Optional.of(new com.sling.sales.deal.common.dto.Pipeline(pipelineResponse.getId(), pipelineResponse.getName(),
              getPipelineStage(pipelineResponse, pipeline)));
        });
  }

  private com.sling.sales.deal.common.dto.PipelineStage getPipelineStage(PipelineResponse pipelineResponse, Pipeline pipeline) {
    return pipelineResponse.getStages()
        .stream()
        .filter(pipelineStageResponse -> pipelineStageResponse.getName().equalsIgnoreCase(pipeline.getStage().getName()))
        .findFirst()
        .map(pipelineStageResponse -> {
          if (ObjectUtils.isEmpty(pipelineStageResponse) || ObjectUtils.isEmpty(pipelineStageResponse.getId()) || ObjectUtils.isEmpty(
              pipelineStageResponse.getName())) {
            throw new DomainException(ErrorCode.PIPELINE_STAGE_INVALID);
          }
          return new com.sling.sales.deal.common.dto.PipelineStage(pipelineStageResponse.getId(),
              pipelineStageResponse.getName());
        })
        .orElseThrow(() -> new DomainException(ErrorCode.PIPELINE_STAGE_INVALID));
  }

  public Mono<List<com.sling.sales.deal.common.dto.Product>> fromProductRequestToProduct(List<Map<String, Object>> productList, String authToken) {
    List<com.sling.sales.deal.api.request.Product> requestedProducts = new ArrayList<>();
    try {
      requestedProducts = productList.stream()
          .map(product -> {
            Map<String, Object> discount = (Map<String, Object>) product.get("discount");
            return new com.sling.sales.deal.api.request.Product((String) product.get("name"), Double.valueOf((String) product.get("quantity")),
                (String) product.get("productPriceCurrency"), ((String) product.get("productPriceValue")),
                new Discount(Type.valueOf(discount.get("type").toString()), Double.parseDouble((String) discount.get("value"))),
                Objects.equals(product.get("units"), "") ? null : (String) product.get("units"));
          }).collect(toList());
    } catch (Exception exception) {
      throw new DomainException(ErrorCode.INVALID_PRODUCTS);
    }
    return getProductsByName(requestedProducts, authToken);
  }

  public List<Deal> findByTenantId(Long tenantId) {
    return dealRepository.findByTenantId(tenantId);
  }

  public Mono<Optional<com.sling.sales.deal.common.dto.Pipeline>> fromPipelineRequestToPipeline(Map<String, Object> pipelinePayload,
      String authToken) {
    if (pipelinePayload.isEmpty()) {
      return Mono.just(Optional.empty());
    }
    Map<String, Object> pipelineStage = (Map<String, Object>) pipelinePayload.get("stage");
    Pipeline pipeline = new Pipeline((String) pipelinePayload.get("name"),
        new com.sling.sales.deal.api.request.PipelineStage((String) pipelineStage.get("name")));
    return getPipelineByName(pipeline, authToken);
  }


  @Transactional
  public void updateEstimatedValueExchangeRateAndActualValueExchangeRate(ExchangeRateHistoryAddedEvent exchangeRateHistoryAddedEvent) {
    long keyToUpdate = exchangeRateHistoryAddedEvent.getCurrency().getId();
    double valueToSet = exchangeRateHistoryAddedEvent.getExchangeRate();
    Long tenantId = exchangeRateHistoryAddedEvent.getTenantId();
    Date fromDate = exchangeRateHistoryAddedEvent.getFromDate();
    Date toDate = exchangeRateHistoryAddedEvent.getToDate();

    String sql = "UPDATE deal "
        + "SET "
        + "    actual_value_exchange_rate = "
        + "    CASE "
        + "        WHEN actual_value_exchange_rate IS NULL OR jsonb_typeof(actual_value_exchange_rate -> '" + keyToUpdate + "') IS NULL THEN "
        + "            COALESCE(actual_value_exchange_rate, CAST('{}' AS jsonb)) || jsonb_build_object('" + keyToUpdate + "', " + valueToSet + ") "
        + "        ELSE "
        + "            actual_value_exchange_rate "
        + "    END, "
        + "    estimated_value_exchange_rate = "
        + "    CASE "
        + "        WHEN estimated_value_exchange_rate IS NULL OR jsonb_typeof(estimated_value_exchange_rate -> '" + keyToUpdate + "') IS NULL THEN "
        + "            COALESCE(estimated_value_exchange_rate,CAST('{}' AS jsonb)) || jsonb_build_object('" + keyToUpdate + "', " + valueToSet + ") "
        + "        ELSE "
        + "            estimated_value_exchange_rate "
        + "    END "
        + "WHERE tenant_id = " + tenantId + " "
        + (toDate != null ? "AND created_at >= '" + fromDate + "' AND created_at < '" + toDate + "'" : "AND created_at >= '" + fromDate + "'");

    entityManager.createNativeQuery(sql).executeUpdate();
    entityManager.close();
  }
}
