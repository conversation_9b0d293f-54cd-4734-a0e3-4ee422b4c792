package com.sling.sales.deal.mq;

import static com.sling.sales.deal.common.dto.EntityType.DEAL;
import static com.sling.sales.deal.mq.event.DealEventPayloadV2.toDealRequestV2;
import static com.sling.sales.deal.mq.event.Metadata.Action.CREATED;
import static com.sling.sales.deal.mq.event.Metadata.Action.UPDATED;
import static java.util.Collections.emptySet;

import com.sling.sales.deal.api.request.DealRequestV2;
import com.sling.sales.deal.domain.deal.Deal;
import com.sling.sales.deal.domain.user.User;
import com.sling.sales.deal.external.currency.TenantCurrencyResponse;
import com.sling.sales.deal.mq.event.DealAssociatedContactEvent;
import com.sling.sales.deal.mq.event.DealCreatedEvent;
import com.sling.sales.deal.mq.event.DealCreatedInternalEvent;
import com.sling.sales.deal.mq.event.DealEventPayloadV2;
import com.sling.sales.deal.mq.event.DealNameUpdatedEvent;
import com.sling.sales.deal.mq.event.DealReassignedEvent;
import com.sling.sales.deal.mq.event.DealUpdatedEvent;
import com.sling.sales.deal.mq.event.DealUpdatedInternalEvent;
import com.sling.sales.deal.mq.event.Metadata;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * Handles internal deal events after database transaction commit.
 * This ensures that RabbitMQ events are only published after the database transaction is successfully committed,
 * preventing race conditions where event listeners try to access data that hasn't been committed yet.
 */
@Component
@Slf4j
public class DealEventTransactionalListener {

  private final DealEventPublisher dealEventPublisher;
  private final DealEventPublisherV2 dealEventPublisherV2;

  @Autowired
  public DealEventTransactionalListener(
      DealEventPublisher dealEventPublisher,
      DealEventPublisherV2 dealEventPublisherV2) {
    this.dealEventPublisher = dealEventPublisher;
    this.dealEventPublisherV2 = dealEventPublisherV2;
  }

  /**
   * Handles deal created events after transaction commit.
   * Publishes all the necessary RabbitMQ events for deal creation.
   */
  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  public void handleDealCreatedEvent(DealCreatedInternalEvent event) {
    Deal deal = event.getDeal();
    User loggedInUser = event.getLoggedInUser();
    List<TenantCurrencyResponse> tenantCurrencies = event.getTenantCurrencies();

    log.info("Publishing deal created events after transaction commit for deal {} tenant {}", 
        deal.getId(), deal.getTenantId());

    try {
      // Publish deal created event
      raiseDealCreatedEvent(deal);
      
      // Publish deal created event V2
      raiseDealCreatedEventV2(deal, loggedInUser.getTenantId(), loggedInUser.getId(), tenantCurrencies);
      
      // Publish deal associated contact event
      raiseDealAssociatedContactEvent(deal, loggedInUser.getId());
      
      // Publish deal reassigned event
      raiseDealReassignedEvent(deal, loggedInUser.getId(), true);

      log.info("Successfully published all deal created events for deal {} tenant {}", 
          deal.getId(), deal.getTenantId());
    } catch (Exception e) {
      log.error("Error publishing deal created events for deal {} tenant {}", 
          deal.getId(), deal.getTenantId(), e);
      // Note: We don't rethrow the exception here to avoid affecting the main transaction
      // The deal is already saved, and we can implement retry mechanisms if needed
    }
  }

  /**
   * Handles deal updated events after transaction commit.
   * Publishes all the necessary RabbitMQ events for deal updates.
   */
  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  public void handleDealUpdatedEvent(DealUpdatedInternalEvent event) {
    Deal deal = event.getDeal();
    User loggedInUser = event.getLoggedInUser();

    log.info("Publishing deal updated events after transaction commit for deal {} tenant {}", 
        deal.getId(), deal.getTenantId());

    try {
      if (event.isDealNameUpdated()) {
        log.info("DealName updated event raised for dealId {} oldName {} and newName {}", 
            deal.getId(), "previous_name", deal.getName());
        raiseDealNameUpdatedEvent(deal);
      }

      if (event.getOldOwnerId() != null && deal.getOwnedBy().getId() != event.getOldOwnerId()) {
        raiseDealReassignedEvent(deal, event.getOldOwnerId(), true);
      }

      raiseDealUpdatedEvent(deal);
      raiseDealUpdatedEventV2(event.getNewDealRequestV2(), event.getExistingDealRequestV2(), 
          event.getMetadata(), UPDATED, loggedInUser, event.isExecuteWorkflow(), 
          event.isSendNotification(), event.getTenantCurrencies());
      raiseDealAssociatedContactEvent(deal, loggedInUser.getId());

      log.info("Successfully published all deal updated events for deal {} tenant {}", 
          deal.getId(), deal.getTenantId());
    } catch (Exception e) {
      log.error("Error publishing deal updated events for deal {} tenant {}", 
          deal.getId(), deal.getTenantId(), e);
      // Note: We don't rethrow the exception here to avoid affecting the main transaction
    }
  }

  private void raiseDealCreatedEvent(Deal deal) {
    // Implementation similar to the original method in DealFacade
    DealCreatedEvent dealCreatedEvent = new DealCreatedEvent(
        deal.getId(),
        deal.getName(),
        deal.getTenantId(),
        deal.getOwnedBy().getId(),
        deal.getOwnedBy().getName(),
        deal.getEstimatedValue().getValue(),
        deal.getEstimatedValue().getCurrencyId(),
        deal.getEstimatedClosureOn(),
        deal.getActualClosureDate(),
        deal.getActualValue() != null ? deal.getActualValue().getValue() : null,
        deal.getActualValue() != null ? deal.getActualValue().getCurrencyId() : null,
        deal.getAssociatedContacts().stream().map(contact -> contact.getId()).collect(Collectors.toList()),
        deal.getAssociatedCompany() != null ? deal.getAssociatedCompany().getId() : null,
        deal.getAssociatedCompany() != null ? deal.getAssociatedCompany().getName() : null,
        deal.getPipelineStages().stream().findFirst().map(stage -> stage.getPipeline().getId()).orElse(null),
        deal.getPipelineStages().stream().findFirst().map(stage -> stage.getPipeline().getName()).orElse(null),
        deal.getPipelineStages().stream().findFirst().map(stage -> stage.getStage().getId()).orElse(null),
        deal.getPipelineStages().stream().findFirst().map(stage -> stage.getStage().getName()).orElse(null),
        deal.getCreatedBy().getId(),
        deal.getCreatedBy().getName(),
        deal.getCreatedAt(),
        deal.getUpdatedBy().getId(),
        deal.getUpdatedBy().getName(),
        deal.getUpdatedAt(),
        deal.getSource() != null ? deal.getSource().getId() : null,
        deal.getSource() != null ? deal.getSource().getName() : null,
        deal.getCampaign() != null ? deal.getCampaign().getId() : null,
        deal.getCampaign() != null ? deal.getCampaign().getName() : null,
        deal.getMetaInfo().getIsNew(),
        deal.getMetaInfo().getLatestActivityCreatedAt(),
        deal.getMetaInfo().getTaskDueOn(),
        deal.getMetaInfo().getMeetingScheduledOn(),
        deal.getSource(),
        deal.getCampaign(), 
        deal.getCustomFieldValues(), 
        deal.getDealProducts().stream().collect(Collectors.toList()), 
        deal.getMetaInfo().getCreatedViaId(),
        deal.getMetaInfo().getCreatedViaName(),
        deal.getMetaInfo().getCreatedViaType(), 
        null, // subSource
        null, // utmSource
        null, // utmCampaign
        null, // utmMedium
        null, // utmContent
        null  // utmTerm
    );
    dealEventPublisher.publishDealCreated(dealCreatedEvent);
  }

  private void raiseDealCreatedEventV2(Deal deal, Long tenantId, Long userId, List<TenantCurrencyResponse> tenantCurrencies) {
    DealRequestV2 dealRequestV2 = toDealRequestV2(deal);
    dealRequestV2.populateCurrencyNameAndDisplayName(tenantCurrencies);
    DealEventPayloadV2 dealEventPayloadV2 = new DealEventPayloadV2(dealRequestV2, null,
        new Metadata(tenantId, userId, DEAL, deal.getId(), CREATED,
            null, emptySet(), null));
    dealEventPublisherV2.publishDealCreatedEventV2(dealEventPayloadV2);
  }

  private void raiseDealAssociatedContactEvent(Deal deal, Long userId) {
    DealAssociatedContactEvent event = new DealAssociatedContactEvent(
        deal.getId(),
        deal.getTenantId(),
        userId,
        deal.getAssociatedContacts()
            .stream()
            .map(contact -> contact.getId())
            .collect(Collectors.toList())
    );
    dealEventPublisher.publishDealAssociatedContactEvent(event);
  }

  private void raiseDealReassignedEvent(Deal deal, Long userId, boolean sendEmail) {
    DealReassignedEvent event = new DealReassignedEvent(
        deal.getId(),
        deal.getTenantId(),
        deal.getOwnedBy().getId(),
        deal.getOwnedBy().getName(),
        userId,
        sendEmail
    );
    dealEventPublisher.publishDealReassignedEvent(event);
  }

  private void raiseDealUpdatedEvent(Deal deal) {
    // Create DealUpdatedEvent similar to original implementation
    DealUpdatedEvent event = new DealUpdatedEvent(
        deal.getId(),
        deal.getName(),
        deal.getTenantId(),
        deal.getOwnedBy().getId(),
        deal.getOwnedBy().getName(),
        deal.getEstimatedValue().getValue(),
        deal.getEstimatedValue().getCurrencyId(),
        deal.getEstimatedClosureOn(),
        deal.getActualClosureDate(),
        deal.getActualValue() != null ? deal.getActualValue().getValue() : null,
        deal.getActualValue() != null ? deal.getActualValue().getCurrencyId() : null,
        deal.getAssociatedContacts().stream().map(contact -> contact.getId()).collect(Collectors.toList()),
        deal.getAssociatedCompany() != null ? deal.getAssociatedCompany().getId() : null,
        deal.getAssociatedCompany() != null ? deal.getAssociatedCompany().getName() : null,
        deal.getPipelineStages().stream().findFirst().map(stage -> stage.getPipeline().getId()).orElse(null),
        deal.getPipelineStages().stream().findFirst().map(stage -> stage.getPipeline().getName()).orElse(null),
        deal.getPipelineStages().stream().findFirst().map(stage -> stage.getStage().getId()).orElse(null),
        deal.getPipelineStages().stream().findFirst().map(stage -> stage.getStage().getName()).orElse(null),
        deal.getCreatedBy().getId(),
        deal.getCreatedBy().getName(),
        deal.getCreatedAt(),
        deal.getUpdatedBy().getId(),
        deal.getUpdatedBy().getName(),
        deal.getUpdatedAt(),
        deal.getSource() != null ? deal.getSource().getId() : null,
        deal.getSource() != null ? deal.getSource().getName() : null,
        deal.getCampaign() != null ? deal.getCampaign().getId() : null,
        deal.getCampaign() != null ? deal.getCampaign().getName() : null,
        deal.getMetaInfo().getIsNew(),
        deal.getMetaInfo().getLatestActivityCreatedAt(),
        deal.getMetaInfo().getTaskDueOn(),
        deal.getMetaInfo().getMeetingScheduledOn(),
        deal.getSource(),
        deal.getCampaign(), 
        deal.getCustomFieldValues(), 
        deal.getDealProducts().stream().collect(Collectors.toList()),
        deal.getMetaInfo().getCreatedViaId(), 
        deal.getMetaInfo().getCreatedViaName(), 
        deal.getMetaInfo().getCreatedViaType(),
        deal.getMetaInfo().getUpdatedViaId(), 
        deal.getMetaInfo().getUpdatedViaName(), 
        deal.getMetaInfo().getUpdatedViaType(),
        null, // subSource
        null, // utmSource
        null, // utmCampaign
        null, // utmMedium
        null, // utmContent
        null  // utmTerm
    );
    dealEventPublisher.publishDealUpdated(event);
  }

  private void raiseDealNameUpdatedEvent(Deal deal) {
    DealNameUpdatedEvent event = new DealNameUpdatedEvent(
        deal.getId(), 
        deal.getName(), 
        deal.getTenantId(), 
        deal.getOwnedBy().getId()
    );
    dealEventPublisher.publishDealNameUpdated(event);
  }

  private void raiseDealUpdatedEventV2(DealRequestV2 newDealRequestV2, DealRequestV2 existingDealRequestV2, 
      Metadata metadata, Metadata.Action action, User loggedInUser, boolean executeWorkflow, 
      boolean sendNotification, List<TenantCurrencyResponse> tenantCurrencies) {
    
    DealEventPayloadV2 dealEventPayloadV2 = null;
    newDealRequestV2.populateCurrencyNameAndDisplayName(tenantCurrencies);
    
    if (metadata == null) {
      dealEventPayloadV2 = new DealEventPayloadV2(newDealRequestV2, existingDealRequestV2,
          new Metadata(loggedInUser.getTenantId(), loggedInUser.getId(), DEAL, newDealRequestV2.getId(), action,
              null, emptySet(), null)
              .withSendNotification(sendNotification)
              .withExecuteWorkflow(executeWorkflow));
    } else {
      Metadata updatedMetadata = metadata.withEntityAction(action).withSendNotification(metadata.isSendNotification())
          .withExecuteWorkflow(metadata.isExecuteWorkflow());
      log.info("passing existing metadata with updated action received from workflow service update request for deal {} with metadata {}",
          updatedMetadata.getEntityId(), updatedMetadata);
      dealEventPayloadV2 = new DealEventPayloadV2(newDealRequestV2, existingDealRequestV2, updatedMetadata);
    }

    dealEventPublisherV2.publishDealUpdatedEventV2(dealEventPayloadV2);
  }
}
