package com.sling.sales.deal.mq.event;

import com.sling.sales.deal.api.response.TenantCurrencyResponse;
import com.sling.sales.deal.domain.deal.Deal;
import com.sling.sales.deal.domain.user.User;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Internal Spring Application Event for deal creation.
 * This event is published within the transaction and handled after transaction commit.
 */
@Data
@AllArgsConstructor
public class DealCreatedInternalEvent {
  private final Deal deal;
  private final User loggedInUser;
  private final List<TenantCurrencyResponse> tenantCurrencies;
}
