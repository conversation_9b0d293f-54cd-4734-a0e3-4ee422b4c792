package com.sling.sales.deal.mq.event;

import com.sling.sales.deal.api.response.TenantCurrencyResponse;
import com.sling.sales.deal.domain.deal.Deal;
import com.sling.sales.deal.domain.user.User;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Internal Spring Application Event for deal updates.
 * This event is published within the transaction and handled after transaction commit.
 */
@Data
@AllArgsConstructor
public class DealUpdatedInternalEvent {
  private final Deal deal;
  private final DealRequestV2 newDealRequestV2;
  private final DealRequestV2 existingDealRequestV2;
  private final Metadata metadata;
  private final User loggedInUser;
  private final boolean executeWorkflow;
  private final boolean sendNotification;
  private final List<TenantCurrencyResponse> tenantCurrencies;
  private final Long oldOwnerId;
  private final boolean dealNameUpdated;
}
